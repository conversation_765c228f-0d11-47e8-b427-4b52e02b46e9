#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构真题统计分析模块
计算知识点频率、重要度、趋势分析，生成统计报告
"""

import os
import json
import csv
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class KnowledgePointStats:
    """知识点统计数据类"""
    category: str
    subcategory: str
    frequency: int  # 出现频率
    total_weight: float  # 总权重
    avg_weight: float  # 平均权重
    years_appeared: List[int]  # 出现的年份
    question_types: Dict[str, int]  # 题型分布
    difficulty_distribution: Dict[str, int]  # 难度分布
    importance_score: float  # 综合重要度得分
    trend_score: float  # 趋势得分
    rank: int  # 排名

@dataclass
class StatisticsReport:
    """统计报告数据类"""
    generation_time: str
    total_questions: int
    total_knowledge_points: int
    years_analyzed: List[int]
    top_knowledge_points: List[KnowledgePointStats]
    category_distribution: Dict[str, Dict[str, Any]]
    yearly_trends: Dict[int, Dict[str, Any]]
    difficulty_analysis: Dict[str, Any]
    type_analysis: Dict[str, Any]
    recommendations: List[str]

class StatisticsAnalyzer:
    """统计分析器类"""
    
    def __init__(self):
        self.analysis_results_dir = "analysis_results"
        self.output_dir = "statistics_output"
        self.ensure_output_dir()
        
    def ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"统计输出目录已准备: {self.output_dir}")
    
    def load_analysis_data(self) -> Dict[int, List[Dict]]:
        """加载内容分析结果数据"""
        logger.info("开始加载内容分析数据")
        
        data = {}
        
        if not os.path.exists(self.analysis_results_dir):
            logger.error(f"分析结果目录不存在: {self.analysis_results_dir}")
            return data
        
        # 加载各年份的题目数据
        for year in [2014, 2015, 2016, 2017, 2018]:
            filename = f"questions_{year}.json"
            filepath = os.path.join(self.analysis_results_dir, filename)
            
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        questions = json.load(f)
                    data[year] = questions
                    logger.info(f"加载{year}年数据: {len(questions)}道题目")
                except Exception as e:
                    logger.error(f"加载{year}年数据失败: {e}")
            else:
                logger.warning(f"文件不存在: {filepath}")
        
        total_questions = sum(len(questions) for questions in data.values())
        logger.info(f"总计加载{total_questions}道题目")
        
        return data
    
    def calculate_frequency(self, data: Dict[int, List[Dict]]) -> Dict[str, KnowledgePointStats]:
        """计算知识点频率统计"""
        logger.info("开始计算知识点频率")
        
        knowledge_stats = defaultdict(lambda: {
            'frequency': 0,
            'total_weight': 0.0,
            'years_appeared': set(),
            'question_types': defaultdict(int),
            'difficulty_distribution': defaultdict(int),
            'weights': []
        })
        
        # 遍历所有题目
        for year, questions in data.items():
            for question in questions:
                question_type = question.get('type', '未知')
                difficulty = question.get('difficulty', '未知')
                knowledge_points = question.get('knowledge_points', [])
                
                for kp in knowledge_points:
                    if len(kp) >= 3:  # 确保有category, subcategory, weight
                        category, subcategory, weight = kp[0], kp[1], kp[2]
                        key = f"{category}-{subcategory}"
                        
                        stats = knowledge_stats[key]
                        stats['frequency'] += 1
                        stats['total_weight'] += weight
                        stats['years_appeared'].add(year)
                        stats['question_types'][question_type] += 1
                        stats['difficulty_distribution'][difficulty] += 1
                        stats['weights'].append(weight)
        
        # 转换为KnowledgePointStats对象
        result = {}
        for key, stats in knowledge_stats.items():
            if '-' in key:
                category, subcategory = key.split('-', 1)
                
                avg_weight = stats['total_weight'] / stats['frequency'] if stats['frequency'] > 0 else 0
                
                kp_stats = KnowledgePointStats(
                    category=category,
                    subcategory=subcategory,
                    frequency=stats['frequency'],
                    total_weight=stats['total_weight'],
                    avg_weight=avg_weight,
                    years_appeared=sorted(list(stats['years_appeared'])),
                    question_types=dict(stats['question_types']),
                    difficulty_distribution=dict(stats['difficulty_distribution']),
                    importance_score=0.0,  # 稍后计算
                    trend_score=0.0,  # 稍后计算
                    rank=0  # 稍后计算
                )
                
                result[key] = kp_stats
        
        logger.info(f"计算完成，共{len(result)}个知识点")
        return result
    
    def calculate_importance_score(self, kp_stats: KnowledgePointStats, total_questions: int) -> float:
        """计算知识点综合重要度得分"""
        # 频率得分 (0-40分)
        frequency_score = min(40, (kp_stats.frequency / total_questions) * 1000)
        
        # 权重得分 (0-30分)
        weight_score = min(30, kp_stats.avg_weight * 10)
        
        # 年份覆盖得分 (0-20分)
        year_coverage = len(kp_stats.years_appeared) / 5  # 总共5年
        coverage_score = year_coverage * 20
        
        # 难度分布得分 (0-10分)
        difficulty_score = 0
        if '困难' in kp_stats.difficulty_distribution:
            difficulty_score += 5
        if '中等' in kp_stats.difficulty_distribution:
            difficulty_score += 3
        if '简单' in kp_stats.difficulty_distribution:
            difficulty_score += 2
        
        total_score = frequency_score + weight_score + coverage_score + difficulty_score
        return min(100, total_score)
    
    def generate_trend_analysis(self, data: Dict[int, List[Dict]], kp_stats: Dict[str, KnowledgePointStats]) -> Dict[str, float]:
        """生成知识点趋势分析"""
        logger.info("开始生成趋势分析")
        
        trend_scores = {}
        
        for key, stats in kp_stats.items():
            # 计算各年份的出现频率
            yearly_freq = {}
            for year in [2014, 2015, 2016, 2017, 2018]:
                yearly_freq[year] = 0
                
                if year in data:
                    for question in data[year]:
                        for kp in question.get('knowledge_points', []):
                            if len(kp) >= 2:
                                kp_key = f"{kp[0]}-{kp[1]}"
                                if kp_key == key:
                                    yearly_freq[year] += 1
            
            # 计算趋势得分
            years = sorted(yearly_freq.keys())
            frequencies = [yearly_freq[year] for year in years]
            
            if len(frequencies) >= 3:
                # 使用线性回归计算趋势
                x = np.array(range(len(frequencies)))
                y = np.array(frequencies)
                
                if np.std(y) > 0:  # 避免除零错误
                    correlation = np.corrcoef(x, y)[0, 1]
                    trend_score = correlation * 50 + 50  # 转换到0-100范围
                else:
                    trend_score = 50  # 中性趋势
            else:
                trend_score = 50
            
            trend_scores[key] = trend_score
        
        return trend_scores
    
    def rank_knowledge_points(self, kp_stats: Dict[str, KnowledgePointStats]) -> List[KnowledgePointStats]:
        """按重要度排序知识点"""
        logger.info("开始排序知识点")
        
        # 按重要度得分排序
        sorted_stats = sorted(kp_stats.values(), key=lambda x: x.importance_score, reverse=True)
        
        # 分配排名
        for i, stats in enumerate(sorted_stats, 1):
            stats.rank = i
        
        logger.info(f"排序完成，共{len(sorted_stats)}个知识点")
        return sorted_stats
    
    def generate_category_distribution(self, kp_stats: Dict[str, KnowledgePointStats]) -> Dict[str, Dict[str, Any]]:
        """生成分类分布统计"""
        category_dist = defaultdict(lambda: {
            'total_frequency': 0,
            'avg_importance': 0,
            'subcategories': [],
            'question_types': defaultdict(int),
            'years_coverage': set()
        })
        
        for stats in kp_stats.values():
            cat_stats = category_dist[stats.category]
            cat_stats['total_frequency'] += stats.frequency
            cat_stats['subcategories'].append(stats.subcategory)
            cat_stats['years_coverage'].update(stats.years_appeared)
            
            for qtype, count in stats.question_types.items():
                cat_stats['question_types'][qtype] += count
        
        # 计算平均重要度
        for category, cat_stats in category_dist.items():
            category_kps = [stats for stats in kp_stats.values() if stats.category == category]
            if category_kps:
                cat_stats['avg_importance'] = sum(kp.importance_score for kp in category_kps) / len(category_kps)
            cat_stats['years_coverage'] = sorted(list(cat_stats['years_coverage']))
            cat_stats['question_types'] = dict(cat_stats['question_types'])
            cat_stats['subcategories'] = list(set(cat_stats['subcategories']))
        
        return dict(category_dist)
    
    def generate_yearly_trends(self, data: Dict[int, List[Dict]]) -> Dict[int, Dict[str, Any]]:
        """生成年度趋势分析"""
        yearly_trends = {}
        
        for year, questions in data.items():
            year_stats = {
                'total_questions': len(questions),
                'type_distribution': defaultdict(int),
                'difficulty_distribution': defaultdict(int),
                'knowledge_categories': defaultdict(int),
                'avg_score': 0
            }
            
            total_score = 0
            for question in questions:
                year_stats['type_distribution'][question.get('type', '未知')] += 1
                year_stats['difficulty_distribution'][question.get('difficulty', '未知')] += 1
                total_score += question.get('score', 0)
                
                for kp in question.get('knowledge_points', []):
                    if len(kp) >= 1:
                        year_stats['knowledge_categories'][kp[0]] += 1
            
            year_stats['avg_score'] = total_score / len(questions) if questions else 0
            year_stats['type_distribution'] = dict(year_stats['type_distribution'])
            year_stats['difficulty_distribution'] = dict(year_stats['difficulty_distribution'])
            year_stats['knowledge_categories'] = dict(year_stats['knowledge_categories'])
            
            yearly_trends[year] = year_stats
        
        return yearly_trends
    
    def generate_recommendations(self, top_kps: List[KnowledgePointStats], category_dist: Dict) -> List[str]:
        """生成学习建议"""
        recommendations = []
        
        # 基于高频知识点的建议
        if top_kps:
            top_5 = top_kps[:5]
            recommendations.append(f"重点关注前5个高频知识点：{', '.join([f'{kp.category}-{kp.subcategory}' for kp in top_5])}")
        
        # 基于分类分布的建议
        sorted_categories = sorted(category_dist.items(), key=lambda x: x[1]['avg_importance'], reverse=True)
        if sorted_categories:
            top_category = sorted_categories[0][0]
            recommendations.append(f"优先学习{top_category}相关内容，该分类平均重要度最高")
        
        # 基于年份覆盖的建议
        consistent_kps = [kp for kp in top_kps if len(kp.years_appeared) >= 4]
        if consistent_kps:
            recommendations.append(f"重点掌握连续多年出现的知识点：{', '.join([f'{kp.category}-{kp.subcategory}' for kp in consistent_kps[:3]])}")
        
        # 基于难度分布的建议
        difficult_kps = [kp for kp in top_kps if '困难' in kp.difficulty_distribution and kp.difficulty_distribution['困难'] > 0]
        if difficult_kps:
            recommendations.append(f"加强练习困难题型涉及的知识点：{', '.join([f'{kp.category}-{kp.subcategory}' for kp in difficult_kps[:3]])}")
        
        return recommendations
    
    def create_statistics_report(self, data: Dict[int, List[Dict]]) -> StatisticsReport:
        """创建完整的统计报告"""
        logger.info("开始创建统计报告")
        
        # 计算基础统计
        kp_stats = self.calculate_frequency(data)
        total_questions = sum(len(questions) for questions in data.values())
        
        # 计算重要度得分
        for stats in kp_stats.values():
            stats.importance_score = self.calculate_importance_score(stats, total_questions)
        
        # 计算趋势得分
        trend_scores = self.generate_trend_analysis(data, kp_stats)
        for key, stats in kp_stats.items():
            stats.trend_score = trend_scores.get(key, 50)
        
        # 排序知识点
        ranked_kps = self.rank_knowledge_points(kp_stats)
        
        # 生成各种分析
        category_dist = self.generate_category_distribution(kp_stats)
        yearly_trends = self.generate_yearly_trends(data)
        
        # 生成难度和题型分析
        difficulty_analysis = self._analyze_difficulty_distribution(data)
        type_analysis = self._analyze_type_distribution(data)
        
        # 生成建议
        recommendations = self.generate_recommendations(ranked_kps, category_dist)
        
        report = StatisticsReport(
            generation_time=datetime.now().isoformat(),
            total_questions=total_questions,
            total_knowledge_points=len(kp_stats),
            years_analyzed=sorted(data.keys()),
            top_knowledge_points=ranked_kps[:20],  # 前20个
            category_distribution=category_dist,
            yearly_trends=yearly_trends,
            difficulty_analysis=difficulty_analysis,
            type_analysis=type_analysis,
            recommendations=recommendations
        )
        
        logger.info("统计报告创建完成")
        return report
    
    def _analyze_difficulty_distribution(self, data: Dict[int, List[Dict]]) -> Dict[str, Any]:
        """分析难度分布"""
        difficulty_stats = defaultdict(int)
        difficulty_by_year = defaultdict(lambda: defaultdict(int))
        
        for year, questions in data.items():
            for question in questions:
                difficulty = question.get('difficulty', '未知')
                difficulty_stats[difficulty] += 1
                difficulty_by_year[year][difficulty] += 1
        
        return {
            'overall_distribution': dict(difficulty_stats),
            'yearly_distribution': {year: dict(dist) for year, dist in difficulty_by_year.items()}
        }
    
    def _analyze_type_distribution(self, data: Dict[int, List[Dict]]) -> Dict[str, Any]:
        """分析题型分布"""
        type_stats = defaultdict(int)
        type_by_year = defaultdict(lambda: defaultdict(int))
        
        for year, questions in data.items():
            for question in questions:
                qtype = question.get('type', '未知')
                type_stats[qtype] += 1
                type_by_year[year][qtype] += 1
        
        return {
            'overall_distribution': dict(type_stats),
            'yearly_distribution': {year: dict(dist) for year, dist in type_by_year.items()}
        }
    
    def export_statistics(self, report: StatisticsReport):
        """导出统计数据为JSON和CSV格式"""
        logger.info("开始导出统计数据")
        
        # 导出JSON格式的完整报告
        json_file = os.path.join(self.output_dir, "statistics_report.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(report), f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"JSON报告已保存: {json_file}")
        
        # 导出CSV格式的知识点统计
        csv_file = os.path.join(self.output_dir, "knowledge_points_stats.csv")
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '排名', '分类', '子分类', '频率', '总权重', '平均权重',
                '出现年份', '重要度得分', '趋势得分', '题型分布', '难度分布'
            ])
            
            # 写入数据
            for kp in report.top_knowledge_points:
                writer.writerow([
                    kp.rank,
                    kp.category,
                    kp.subcategory,
                    kp.frequency,
                    f"{kp.total_weight:.2f}",
                    f"{kp.avg_weight:.2f}",
                    ','.join(map(str, kp.years_appeared)),
                    f"{kp.importance_score:.2f}",
                    f"{kp.trend_score:.2f}",
                    str(kp.question_types),
                    str(kp.difficulty_distribution)
                ])
        
        logger.info(f"CSV统计已保存: {csv_file}")
        
        # 导出分类汇总CSV
        category_csv = os.path.join(self.output_dir, "category_summary.csv")
        with open(category_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['分类', '总频率', '平均重要度', '子分类数量', '年份覆盖'])
            
            for category, stats in report.category_distribution.items():
                writer.writerow([
                    category,
                    stats['total_frequency'],
                    f"{stats['avg_importance']:.2f}",
                    len(stats['subcategories']),
                    ','.join(map(str, stats['years_coverage']))
                ])
        
        logger.info(f"分类汇总已保存: {category_csv}")
    
    def run_complete_analysis(self) -> StatisticsReport:
        """运行完整的统计分析"""
        logger.info("=== 开始完整统计分析 ===")
        
        # 加载数据
        data = self.load_analysis_data()
        if not data:
            logger.error("无法加载分析数据")
            return None
        
        # 创建统计报告
        report = self.create_statistics_report(data)
        
        # 导出结果
        self.export_statistics(report)
        
        logger.info("=== 统计分析完成 ===")
        return report


def test_statistics_analyzer():
    """测试统计分析器"""
    print("=== 统计分析器测试 ===")
    
    analyzer = StatisticsAnalyzer()
    
    # 运行完整分析
    report = analyzer.run_complete_analysis()
    
    if report:
        print(f"分析完成!")
        print(f"总题目数: {report.total_questions}")
        print(f"总知识点数: {report.total_knowledge_points}")
        print(f"分析年份: {report.years_analyzed}")
        
        print(f"\n前5个重要知识点:")
        for i, kp in enumerate(report.top_knowledge_points[:5], 1):
            print(f"  {i}. {kp.category}-{kp.subcategory} (重要度: {kp.importance_score:.1f})")
        
        print(f"\n学习建议:")
        for i, rec in enumerate(report.recommendations, 1):
            print(f"  {i}. {rec}")
        
        return True
    else:
        print("分析失败!")
        return False


if __name__ == "__main__":
    success = test_statistics_analyzer()
    print(f"\n统计分析器{'测试成功' if success else '测试失败'}!")
