#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能题目分析器
从PDF提取的内容中识别题目类型、提取知识点、分析难度
"""

import json
import re
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from knowledge_base import knowledge_base

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Question:
    """题目数据类"""
    id: str
    type: str  # 题目类型
    content: str  # 题目内容
    options: List[str]  # 选择题选项
    section: str  # 所属大题
    knowledge_points: List[Tuple[str, str, float]]  # (主分类, 子分类, 权重)
    difficulty: str  # 难度等级
    year: int  # 年份
    raw_text: str  # 原始文本

class QuestionAnalyzer:
    """题目分析器类"""
    
    def __init__(self):
        self.knowledge_base = knowledge_base
        self.question_patterns = self._build_question_patterns()
        self.difficulty_indicators = self._build_difficulty_indicators()
    
    def _build_question_patterns(self) -> Dict[str, List[str]]:
        """构建题目类型识别模式"""
        return {
            "填空题": [
                "填空题", "填空", "空白", "______", "_____", "___",
                "在.*填入", "补充完整", "空格"
            ],
            "选择题": [
                "选择题", "单选", "多选", "选择",
                "A.", "B.", "C.", "D.",
                "A、", "B、", "C、", "D、",
                "下列.*正确", "以下.*错误", "关于.*说法"
            ],
            "简答题": [
                "简答题", "问答题", "解答题", "简述", "说明", "解释",
                "什么是", "如何", "为什么", "比较", "分析"
            ],
            "算法题": [
                "算法题", "设计题", "编程题", "算法",
                "设计.*算法", "实现.*算法", "写出.*算法",
                "伪代码", "时间复杂度", "空间复杂度"
            ],
            "计算题": [
                "计算题", "求解", "计算", "构造", "画出", "绘制",
                "给定.*求", "已知.*计算", "建立", "写出"
            ]
        }
    
    def _build_difficulty_indicators(self) -> Dict[str, List[str]]:
        """构建难度指示词"""
        return {
            "简单": [
                "基本", "简单", "直接", "定义", "概念",
                "是什么", "列举", "写出", "说出"
            ],
            "中等": [
                "分析", "比较", "设计", "实现", "应用",
                "如何", "为什么", "构造", "建立"
            ],
            "困难": [
                "优化", "证明", "复杂", "综合", "创新",
                "算法设计", "复杂度分析", "系统设计"
            ]
        }
    
    def parse_questions(self, extracted_data: Dict) -> List[Question]:
        """解析PDF提取数据中的题目"""
        logger.info("开始解析题目")
        
        all_questions = []
        
        for filename, file_data in extracted_data.get('files', {}).items():
            if file_data.get('status') != 'success':
                continue
            
            year = self._extract_year_from_filename(filename)
            text = file_data['extraction_info']['text']
            
            # 解析题目
            questions = self._parse_text_questions(text, filename, year)
            all_questions.extend(questions)
            
            logger.info(f"{filename}: 解析出 {len(questions)} 道题目")
        
        logger.info(f"总计解析出 {len(all_questions)} 道题目")
        return all_questions
    
    def _extract_year_from_filename(self, filename: str) -> int:
        """从文件名提取年份"""
        match = re.search(r'(\d{4})', filename)
        return int(match.group(1)) if match else 2024
    
    def _parse_text_questions(self, text: str, filename: str, year: int) -> List[Question]:
        """从文本中解析题目"""
        questions = []
        
        # 清理和预处理文本
        clean_text = self._clean_text(text)
        
        # 按大题分割
        sections = self._split_by_sections(clean_text)
        
        for section_title, section_content in sections:
            section_questions = self._parse_section_questions(
                section_content, section_title, filename, year
            )
            questions.extend(section_questions)
        
        return questions
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        # 移除过多的短横线
        text = re.sub(r'-{3,}', '', text)
        # 标准化标点
        text = text.replace('（', '(').replace('）', ')')
        return text.strip()
    
    def _split_by_sections(self, text: str) -> List[Tuple[str, str]]:
        """按大题分割文本"""
        sections = []
        
        # 查找大题标题
        section_pattern = r'([一二三四五六七八九十])、?\s*([^一二三四五六七八九十]*?)(?=[一二三四五六七八九十]、|$)'
        matches = re.finditer(section_pattern, text, re.DOTALL)
        
        for match in matches:
            section_num = match.group(1)
            section_content = match.group(2).strip()
            
            # 提取题型
            section_title = self._extract_section_title(section_content)
            if not section_title:
                section_title = f"第{section_num}题"
            
            sections.append((section_title, section_content))
        
        # 如果没有找到大题，整体作为一个部分
        if not sections:
            sections.append(("综合题", text))
        
        return sections
    
    def _extract_section_title(self, content: str) -> str:
        """提取大题标题"""
        # 查找题型关键词
        for question_type in self.question_patterns.keys():
            if question_type in content[:50]:  # 在前50字符中查找
                return question_type
        return ""
    
    def _parse_section_questions(self, content: str, section_title: str, 
                                filename: str, year: int) -> List[Question]:
        """解析大题中的小题"""
        questions = []
        
        # 按小题分割
        question_parts = self._split_questions(content)
        
        for i, (question_num, question_content) in enumerate(question_parts):
            if len(question_content.strip()) < 10:  # 过滤过短内容
                continue
            
            question_id = f"{year}_{filename}_{section_title}_{i+1}"
            
            # 创建题目对象
            question = Question(
                id=question_id,
                type=self.identify_question_type(question_content, section_title),
                content=self._extract_question_content(question_content),
                options=self._extract_options(question_content),
                section=section_title,
                knowledge_points=self.extract_knowledge_points(question_content),
                difficulty=self.analyze_difficulty(question_content),
                year=year,
                raw_text=question_content
            )
            
            questions.append(question)
        
        return questions
    
    def _split_questions(self, content: str) -> List[Tuple[str, str]]:
        """分割小题"""
        questions = []
        
        # 多种题号模式
        patterns = [
            r'(\d+)、\s*([^0-9]*?)(?=\d+、|$)',  # 1、题目内容
            r'(\d+)\.\s*([^0-9]*?)(?=\d+\.|$)',  # 1. 题目内容
            r'(\d+)\)\s*([^0-9]*?)(?=\d+\)|$)',  # 1) 题目内容
        ]
        
        for pattern in patterns:
            matches = list(re.finditer(pattern, content, re.DOTALL))
            if matches:
                for match in matches:
                    question_num = match.group(1)
                    question_content = match.group(2).strip()
                    if question_content:
                        questions.append((question_num, question_content))
                break
        
        # 如果没有找到小题，整体作为一题
        if not questions:
            questions.append(("1", content))
        
        return questions
    
    def identify_question_type(self, content: str, section_title: str) -> str:
        """识别题目类型"""
        content_lower = content.lower()
        
        # 首先根据大题标题判断
        for qtype, patterns in self.question_patterns.items():
            if qtype in section_title:
                return qtype
        
        # 根据内容特征判断
        type_scores = {}
        
        for qtype, patterns in self.question_patterns.items():
            score = 0
            for pattern in patterns:
                if pattern.lower() in content_lower or pattern in content:
                    score += 1
            type_scores[qtype] = score
        
        # 特殊规则
        if re.search(r'[ABCD][\.\)、]\s*', content):
            type_scores["选择题"] = type_scores.get("选择题", 0) + 3
        
        if re.search(r'_{3,}', content):
            type_scores["填空题"] = type_scores.get("填空题", 0) + 3
        
        # 返回得分最高的类型
        if type_scores:
            return max(type_scores.items(), key=lambda x: x[1])[0]
        else:
            return "简答题"
    
    def _extract_question_content(self, content: str) -> str:
        """提取题目主要内容"""
        # 移除选项部分
        content = re.sub(r'[ABCD][\.\)、]\s*[^\n]*', '', content)
        # 移除多余空白
        content = re.sub(r'\s+', ' ', content).strip()
        return content[:200] + "..." if len(content) > 200 else content
    
    def _extract_options(self, content: str) -> List[str]:
        """提取选择题选项"""
        options = []
        option_pattern = r'([ABCD])[\.\)、]\s*([^\n\r]+)'
        matches = re.findall(option_pattern, content)
        
        for letter, text in matches:
            options.append(f"{letter}. {text.strip()}")
        
        return options
    
    def extract_knowledge_points(self, content: str) -> List[Tuple[str, str, float]]:
        """提取知识点"""
        analysis = self.knowledge_base.analyze_text_knowledge(content)
        return analysis["knowledge_points"][:5]  # 返回前5个最重要的
    
    def analyze_difficulty(self, content: str) -> str:
        """分析题目难度"""
        difficulty_scores = {"简单": 0, "中等": 0, "困难": 0}
        
        content_lower = content.lower()
        
        for difficulty, indicators in self.difficulty_indicators.items():
            for indicator in indicators:
                if indicator.lower() in content_lower:
                    difficulty_scores[difficulty] += 1
        
        # 基于长度调整
        if len(content) > 300:
            difficulty_scores["困难"] += 1
        elif len(content) > 150:
            difficulty_scores["中等"] += 1
        else:
            difficulty_scores["简单"] += 1
        
        # 基于知识点复杂度
        knowledge_points = self.extract_knowledge_points(content)
        if knowledge_points:
            avg_weight = sum(weight for _, _, weight in knowledge_points) / len(knowledge_points)
            if avg_weight > 0.3:
                difficulty_scores["困难"] += 1
            elif avg_weight > 0.2:
                difficulty_scores["中等"] += 1
        
        return max(difficulty_scores.items(), key=lambda x: x[1])[0]
    
    def batch_analyze_questions(self) -> Dict:
        """批量分析所有题目"""
        logger.info("=== 开始批量题目分析 ===")
        
        # 加载PDF提取数据
        try:
            with open('extracted_data.json', 'r', encoding='utf-8') as f:
                extracted_data = json.load(f)
        except FileNotFoundError:
            logger.error("未找到extracted_data.json文件")
            return {}
        
        # 解析题目
        questions = self.parse_questions(extracted_data)
        
        # 生成分析报告
        analysis_report = self._generate_analysis_report(questions)
        
        # 保存结果
        self._save_analysis_results(questions, analysis_report)
        
        logger.info("=== 题目分析完成 ===")
        return analysis_report
    
    def _generate_analysis_report(self, questions: List[Question]) -> Dict:
        """生成分析报告"""
        report = {
            "total_questions": len(questions),
            "by_year": {},
            "by_type": {},
            "by_difficulty": {},
            "by_section": {},
            "knowledge_distribution": {},
            "questions_data": [asdict(q) for q in questions]
        }
        
        # 按年份统计
        for question in questions:
            year = question.year
            report["by_year"][year] = report["by_year"].get(year, 0) + 1
        
        # 按类型统计
        for question in questions:
            qtype = question.type
            report["by_type"][qtype] = report["by_type"].get(qtype, 0) + 1
        
        # 按难度统计
        for question in questions:
            difficulty = question.difficulty
            report["by_difficulty"][difficulty] = report["by_difficulty"].get(difficulty, 0) + 1
        
        # 按大题统计
        for question in questions:
            section = question.section
            report["by_section"][section] = report["by_section"].get(section, 0) + 1
        
        # 知识点分布
        for question in questions:
            for main_cat, sub_cat, weight in question.knowledge_points:
                key = f"{main_cat}-{sub_cat}"
                if key not in report["knowledge_distribution"]:
                    report["knowledge_distribution"][key] = {"count": 0, "total_weight": 0}
                report["knowledge_distribution"][key]["count"] += 1
                report["knowledge_distribution"][key]["total_weight"] += weight
        
        return report
    
    def _save_analysis_results(self, questions: List[Question], report: Dict):
        """保存分析结果"""
        # 保存详细结果
        with open('questions_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info("分析结果已保存到 questions_analysis.json")


def test_question_analyzer():
    """测试题目分析器"""
    print("=== 题目分析器测试 ===")
    
    analyzer = QuestionAnalyzer()
    
    # 测试题目类型识别
    test_questions = [
        "1、当线性表的元素总数基本稳定，且很少进行插入和删除操作时，应采用_____存储结构。",
        "2、设计一个判别表达式中左、右括号是否配对出现的算法，采用（d）数据结构最佳。A. 线性表 B. 队列 C. 链表 D. 栈",
        "3、简述二叉搜索树的性质和特点。",
        "4、设计一个算法，实现二分查找，并分析其时间复杂度。"
    ]
    
    for i, content in enumerate(test_questions, 1):
        print(f"\n测试题目 {i}: {content[:50]}...")
        
        qtype = analyzer.identify_question_type(content, "")
        knowledge_points = analyzer.extract_knowledge_points(content)
        difficulty = analyzer.analyze_difficulty(content)
        
        print(f"  类型: {qtype}")
        print(f"  难度: {difficulty}")
        print(f"  知识点: {[f'{cat}-{sub}' for cat, sub, _ in knowledge_points[:2]]}")
    
    return True


if __name__ == "__main__":
    success = test_question_analyzer()
    print(f"\n题目分析器{'测试成功' if success else '测试失败'}!")
    
    if success:
        # 运行批量分析
        analyzer = QuestionAnalyzer()
        report = analyzer.batch_analyze_questions()
        
        if report:
            print(f"\n批量分析完成:")
            print(f"  总题目数: {report['total_questions']}")
            print(f"  题型分布: {report['by_type']}")
            print(f"  难度分布: {report['by_difficulty']}")
