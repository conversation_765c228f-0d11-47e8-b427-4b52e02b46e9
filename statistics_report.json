{"total_questions": 64, "total_knowledge_points": 13, "years_analyzed": ["2014", "2015", "2016", "2017", "2018"], "top_knowledge_points": [{"category": "线性表", "subcategory": "链表", "frequency": 6, "total_weight": 3.25, "avg_weight": 0.5416666666666666, "years_appeared": [2014, 2016, 2017, 2018], "question_types": {"选择题": 1, "填空题": 4, "算法题": 1}, "difficulty_distribution": {"简单": 4, "中等": 1, "困难": 1}, "importance_score": 71.83333333333333, "rank": 1}, {"category": "树结构", "subcategory": "哈夫曼树", "frequency": 7, "total_weight": 3.25, "avg_weight": 0.4642857142857143, "years_appeared": [2014, 2015, 2016, 2017, 2018], "question_types": {"计算题": 3, "简答题": 4}, "difficulty_distribution": {"简单": 5, "中等": 2}, "importance_score": 70.08928571428572, "rank": 2}, {"category": "树结构", "subcategory": "堆", "frequency": 7, "total_weight": 3.5, "avg_weight": 0.5, "years_appeared": [2014, 2016, 2017, 2018], "question_types": {"填空题": 3, "计算题": 3, "选择题": 1}, "difficulty_distribution": {"简单": 6, "困难": 1}, "importance_score": 69.875, "rank": 3}, {"category": "图结构", "subcategory": "图表示", "frequency": 10, "total_weight": 3.2000000000000006, "avg_weight": 0.32000000000000006, "years_appeared": [2014, 2015, 2016, 2018], "question_types": {"计算题": 3, "填空题": 1, "简答题": 6}, "difficulty_distribution": {"简单": 9, "中等": 1}, "importance_score": 68.25, "rank": 4}, {"category": "查找算法", "subcategory": "哈希表", "frequency": 6, "total_weight": 2.7300000000000004, "avg_weight": 0.45500000000000007, "years_appeared": [2014, 2015, 2016, 2017, 2018], "question_types": {"简答题": 4, "计算题": 2}, "difficulty_distribution": {"中等": 3, "简单": 3}, "importance_score": 66.5, "rank": 5}, {"category": "排序算法", "subcategory": "高级排序", "frequency": 9, "total_weight": 1.8199999999999996, "avg_weight": 0.2022222222222222, "years_appeared": [2014, 2015, 2016, 2017, 2018], "question_types": {"填空题": 3, "选择题": 4, "计算题": 2}, "difficulty_distribution": {"简单": 8, "困难": 1}, "importance_score": 65.23611111111111, "rank": 6}, {"category": "树结构", "subcategory": "遍历", "frequency": 5, "total_weight": 1.625, "avg_weight": 0.325, "years_appeared": [2014, 2015, 2016, 2018], "question_types": {"计算题": 2, "简答题": 3}, "difficulty_distribution": {"简单": 5}, "importance_score": 49.875, "rank": 7}, {"category": "排序算法", "subcategory": "简单排序", "frequency": 2, "total_weight": 0.7000000000000001, "avg_weight": 0.35000000000000003, "years_appeared": [2015, 2018], "question_types": {"选择题": 2}, "difficulty_distribution": {"简单": 2}, "importance_score": 33.75, "rank": 8}, {"category": "排序算法", "subcategory": "线性排序", "frequency": 3, "total_weight": 0.30000000000000004, "avg_weight": 0.10000000000000002, "years_appeared": [2014, 2016, 2018], "question_types": {"填空题": 1, "选择题": 1, "计算题": 1}, "difficulty_distribution": {"简单": 3}, "importance_score": 28.375, "rank": 9}, {"category": "树结构", "subcategory": "二叉树", "frequency": 1, "total_weight": 0.35, "avg_weight": 0.35, "years_appeared": [2014], "question_types": {"填空题": 1}, "difficulty_distribution": {"简单": 1}, "importance_score": 26.625, "rank": 10}, {"category": "线性表", "subcategory": "数组", "frequency": 1, "total_weight": 0.3, "avg_weight": 0.3, "years_appeared": [2018], "question_types": {"计算题": 1}, "difficulty_distribution": {"简单": 1}, "importance_score": 24.125, "rank": 11}, {"category": "算法分析", "subcategory": "复杂度", "frequency": 1, "total_weight": 0.12, "avg_weight": 0.12, "years_appeared": [2018], "question_types": {"填空题": 1}, "difficulty_distribution": {"简单": 1}, "importance_score": 15.125, "rank": 12}, {"category": "排序算法", "subcategory": "排序分析", "frequency": 1, "total_weight": 0.1, "avg_weight": 0.1, "years_appeared": [2018], "question_types": {"填空题": 1}, "difficulty_distribution": {"简单": 1}, "importance_score": 14.125, "rank": 13}], "yearly_trends": {"2014": {"total_questions": 19, "type_distribution": {"填空题": 10, "计算题": 4, "简答题": 2, "选择题": 2, "算法题": 1}, "difficulty_distribution": {"简单": 15, "中等": 4}, "knowledge_categories": {"树结构": 5, "排序算法": 2, "查找算法": 1, "图结构": 3, "线性表": 2}, "avg_knowledge_points": 0.6842105263157895}, "2015": {"total_questions": 9, "type_distribution": {"填空题": 4, "选择题": 1, "计算题": 3, "简答题": 1}, "difficulty_distribution": {"简单": 9}, "knowledge_categories": {"排序算法": 2, "树结构": 2, "图结构": 2, "查找算法": 1}, "avg_knowledge_points": 0.7777777777777778}, "2016": {"total_questions": 19, "type_distribution": {"填空题": 6, "选择题": 2, "简答题": 8, "计算题": 2, "算法题": 1}, "difficulty_distribution": {"简单": 15, "中等": 2, "困难": 2}, "knowledge_categories": {"树结构": 7, "排序算法": 5, "查找算法": 2, "图结构": 3, "线性表": 2}, "avg_knowledge_points": 1.0}, "2017": {"total_questions": 8, "type_distribution": {"填空题": 4, "选择题": 1, "简答题": 1, "计算题": 2}, "difficulty_distribution": {"简单": 6, "中等": 1, "困难": 1}, "knowledge_categories": {"树结构": 3, "排序算法": 1, "查找算法": 1, "线性表": 1}, "avg_knowledge_points": 0.75}, "2018": {"total_questions": 9, "type_distribution": {"填空题": 3, "选择题": 1, "简答题": 3, "计算题": 2}, "difficulty_distribution": {"简单": 8, "中等": 1}, "knowledge_categories": {"树结构": 3, "排序算法": 5, "算法分析": 1, "查找算法": 1, "线性表": 2, "图结构": 2}, "avg_knowledge_points": 1.5555555555555556}}, "difficulty_analysis": {"overall_distribution": {"简单": 53, "中等": 8, "困难": 3}, "by_question_type": {"填空题": {"简单": 23, "中等": 2, "困难": 2}, "计算题": {"简单": 12, "困难": 1}, "简答题": {"中等": 5, "简单": 10}, "选择题": {"简单": 7}, "算法题": {"中等": 1, "简单": 1}}, "by_year": {"2014": {"简单": 15, "中等": 4}, "2015": {"简单": 9}, "2016": {"简单": 15, "中等": 2, "困难": 2}, "2017": {"简单": 6, "中等": 1, "困难": 1}, "2018": {"简单": 8, "中等": 1}}}, "type_analysis": {"overall_distribution": {"填空题": 27, "计算题": 13, "简答题": 15, "选择题": 7, "算法题": 2}, "by_year": {"2014": {"填空题": 10, "计算题": 4, "简答题": 2, "选择题": 2, "算法题": 1}, "2015": {"填空题": 4, "选择题": 1, "计算题": 3, "简答题": 1}, "2016": {"填空题": 6, "选择题": 2, "简答题": 8, "计算题": 2, "算法题": 1}, "2017": {"填空题": 4, "选择题": 1, "简答题": 1, "计算题": 2}, "2018": {"填空题": 3, "选择题": 1, "简答题": 3, "计算题": 2}}, "by_difficulty": {"简单": {"填空题": 23, "计算题": 12, "选择题": 7, "简答题": 10, "算法题": 1}, "中等": {"简答题": 5, "填空题": 2, "算法题": 1}, "困难": {"填空题": 2, "计算题": 1}}}, "recommendations": ["🎯 重点掌握前5个高频考点：线性表-链表, 树结构-哈夫曼树, 树结构-堆, 图结构-图表示, 查找算法-哈希表", "📈 重点关注连续多年出现的知识点：线性表-链表, 树结构-哈夫曼树, 树结构-堆", "💪 加强练习困难题型涉及的知识点：线性表-链表, 树结构-堆, 排序算法-高级排序", "📝 填空题是最常见题型(27道)，需重点练习", "⏰ 建议复习时间分配：高频考点60%，中频考点30%，低频考点10%", "📚 复习策略：先理解概念，再练习题目，最后总结规律"]}