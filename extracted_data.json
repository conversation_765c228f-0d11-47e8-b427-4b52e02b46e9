{"extraction_summary": {"total_files": 7, "successful_extractions": 7, "failed_extractions": 0, "total_questions": 0}, "files": {"数据结构2014A.pdf": {"extraction_info": {"filepath": "数据结构2014A.pdf", "method": "pdfplumber", "text": "中国海洋大学全日制本科课程期末考试试卷\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n--- 2014年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n---\n号 : - - - - - -\n座--- 试卷类型： A 卷 命题人: 审核人：________\n---\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n-\n-\n-\n- ______________。\n---\n号 : - - - - - -\n室---\n线线线 题号 一 二 三 四 五 六 七 八 九 总分\n教\n---\n场---\n---\n考--- 得分\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 - -\n-\n- -\n-\n- -\n- 一、填空题(15分，每空 1.5分)\n海--- 1、当线性表的元素总数基本稳定，且很少进行插入和删除操作，但要求以最快的速度存取线性\n---\n：\n张\n- -\n-\n- -\n- -\n-\n- -\n- -\n-\n- -\n2、\n表\n对\n中\n于双\n的\n向\n元\n链\n素\n表\n时，\n,在\n应\n两\n采\n个\n用\n结\n_\n点\n__\n之\n_顺\n间\n序\n插\n_\n入\n__\n一\n存\n个\n储\n新\n结\n结\n构\n点\n。\n需 修改的指针数共 ___4___个。\n师---\n--- 3、对于一个具有 n 个结点的单链表，在给定值为 x 的结点后插入一个新结点的时间复杂度为\n教---\n课- - - - - - o（n）。\n授-\n- - 4、串是一种特殊的线性表，其特殊性表现在组成串的数据元素只能是________。\n---\n---\n订订订 5、所谓稀疏矩阵指的是_________________ _____________________。\n---\n--- 6、广义表（a,(b,c),d,e）的表尾为_________（（b，c），d，e）____________。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n7、线索二叉树中结点的左线索指向其_____前驱结点________。\n---\n--- 8、Prim（普里姆）算法适用于求________ ____的网的最小生成树；\n---\n级 :- - - - - - 9、算法的时间复杂度是_______ __________________________。\n---\n年--- 10、树的后序遍历序列与其对应的二叉树__中序____遍历序列相同。\n业\n专- -\n-\n- -\n-\n- -\n-\n二、选择题(28 分，每题 2分)\n- - - - - - 1、设计一个判别表达式中左、右括号是否配对出现的算法，采用（ d ）数据结构最佳。\n---\n--- A．线性表的顺序存储结构 B. 队列\n-\n-\n-\n-\n-\n-\n-\n-\n-\nC. 线性表的链式存储结构 D. 栈\n---\n-\n-\n-\n-\n-\n-\n名 : -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n装装装\n2、设\nA\n有\n．\n三\nXY\n个\nZ\n元 素 X， Y ，Z\nB.\n顺\nY\n序\nZX\n进 栈 （ 进 的 过\nC.\n程\nZ\n中\nXY\n允 许 出 栈\nD.\n）\nZ\n，\nY\n不\nX\n能得到的出栈序列是( c )。\n姓\n-\n-\n-\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n3 、 最 大\nA．\n容\nr\n量\nea\n为\nr=f\nn\nr\n的\non\n循\nt\n环 队 列 ， 队 尾 指\nB.\n针\n(\n是\nre\nr\na\ne\nr\na\n+\nr\n1\n，\n)\n队\nMO\n头\nD\n是\nn=f\nf\nr\nr\no\no\nn\nn\nt\nt ，则队空的条件是（ a ）。\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n- C．rear+1=front D. (rear-l) MOD n=front\n---\n-\n-\n-\n-\n-\n-\n号\n:---\n学\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第1页 共3页 ＋\n4、以行序为主序方式，将n阶对称矩阵A的下三角形的元素(包括主对角线上所有元素)依次存\n放于一维数组B［0..(n(n+1))/2-1］中，则在B中确定a（i<j）的位置k的关系为( d )。\nij\nA. i*(i-1)/2+j B. j*(j-1)/2+i C. i*(i+1)/2+j D. j*(j+1)/2+i\n5、深度为h的满m叉树的第k层有（ a ）个结点。(1=<k=<h)\nA．mk-1 B．mk-1 C．mh-1 D．mh-1\n6、若一棵二叉树具有9个度为2的结点，5个度为1的结点，则度为0的结点个数是（ b ）。\nA．9 B．10 C．15 D．不确定\n7、下述二叉树中,哪一种满足性质:从任一结点出发到根的路径上所经过的结点序列按其关键字\n有序（b）。\nA．二叉排序树 B．哈夫曼树 C．AVL树 D．堆\n8、 在有向图 G 的拓扑序列中，若顶点 Vi 在顶点 Vj 之前，则下列情形不可能出现的是\n（ d ）。\nA．G中有弧<Vi,Vj> B．G中有一条从Vi到Vj的路径\nC．G中没有弧<Vi,Vj> D．G中有一条从Vj到Vi的路径\n9、下面关于求关键路径的说法不正确的是（ c ）。\nA．求关键路径是以拓扑排序为基础的\nB．一个事件的最早开始时间与以该事件为尾的弧的活动最早开始时间相同\nC．一个事件的最迟开始时间为以该事件为尾的弧的活动最迟开始时间与该活动的持续\n时间之差\nD．关键活动一定位于关键路径上\n10、具有12个关键字的有序表，折半查找的平均查找长度为（ a ）。\nA. 3 B. 4 C. 2.5 D. 5\n11、下列关于m阶B-树的说法错误的是( c ) 。\nA．根结点至多有m棵子树 B．所有叶子都在同一层次上\nC. 非叶子结点至少有m/2 (m为偶数)或m/2+1（m为奇数）棵子树\nD. 根结点中的数据是有序的\n12、在下列排序算法中,哪一个算法的时间复杂度与初始序列无关（ d ）。\nA． 直接插入排序 B. 冒泡排序 C. 快速排序 D. 直接选择排序\n13、下列排序算法中，( c )排序在一趟结束后不一定能选出一个元素放在其最终位置上。\nA. 选择 B. 冒泡 C. 归并 D. 堆\n14、有一随机数组(25,84,21,46,13,27,68,35,20),采用某种方法对它们进行排序,其每趟排序\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第2页 共3页 ＋\n结果如下, 则该排序方法是（ b ）。\n初 始:25,84,21,46,13,27,68,35,20 第一趟:20,13,21,25,46,27,68,35,84\n第二趟:13,20,21,25,35,27,46,68,84 第三趟:13,20,21,25,27,35,46,68,84\nA．基数排序 B．快速排序 C．堆排序 D． 起泡排序\n三、（6分）已知某二叉树的后序遍历和中序遍历如下，构造出该二叉树。\n后序遍历序列： G D B E I H F C A 中序遍历序列 ：D G B A E C H I F\n由二叉树的前序遍历和后序遍历结果能否唯一确定一棵二叉树？\n四、（6分） 若叶子结点的权值分别为1,2,3,4,5,6。请构造一棵哈曼夫树，并计算该哈曼\n夫树的带权路径长度WPL。\n五、（8分）\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - （\n（\n1\n2\n）\n）\n输\n指\n入\n出\n序\n二\n列\n叉\n(\n排\n46\n序\n,8\n树\n8,\n与\n45\n平\n,3\n衡\n9,\n二\n70\n叉\n,5\n树\n8,\n的\n10\n区\n1,\n别\n10\n。\n,6 6,34)，给出构造一棵二叉排序树的步骤。\n---\n-\n-\n-\n-\n-\n-\n号 : - - - - - - 六、（10 分）设哈希函数 H(k)=K mod 7, 哈希表的地址空间为 0～6，对关键字序列\n座---\n---\n--- {32,13,49,18,22,38,21}按链地址法处理冲突的办法构造哈希表，并指出查找关键字 21\n---\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 需 要进行几次比较。\n---\n: - - - - - - 七、（12分）对下图中的带权有向图，\n号---\n室- -\n-\n-\n-\n- 1）写出其邻接表结构；\n教---\n线线线 2）根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；\n场\n考- - - - - - 3）利用Dijkstra算法，求图中顶点a到其它各顶点的最短路径，写出执行的过程。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n--- a\n---\n燕 -\n-\n- -\n-\n-\n- -\n-\n-\n- - 10 C 100\n海- - - - - - 30 e\n--- b\n张---\nD\n： - - - - - - - - - - - - B 5 0 10 60\n---\n师---\n教- - - - - - c d\n课\n授-\n-\n-\n-\n-\n- 20 G\n---\n---\n订订订\n八、（10 分）设计算法将一个带头结点的单链表 A 分解为两个带头结点的单链表 B、C，其\n--- 中B表中的结点为A表值小于0的结点，而C表中的结点为A表值大于等于0的结点（链\n- -\n-\n- -\n-\n- -\n-\n表A的元素类型为整型，要求B、C表利用A表的结点进行存储）\n---\n--- 九、（5分）\n---\n级\n:- - -\n-\n- -\n-\n- - 假设非空二叉树采用二叉链表存储，请设计一个递归算法，输出二叉树中度为2的结点数目。\n---\n年---\n业\n专- -\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第3页 共3页 ＋\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: 装装装\n名\n---\n姓---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n号\n:---\n学", "pages": [{"page_number": 1, "text": "中国海洋大学全日制本科课程期末考试试卷\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n--- 2014年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n---\n号 : - - - - - -\n座--- 试卷类型： A 卷 命题人: 审核人：________\n---\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n-\n-\n-\n- ______________。\n---\n号 : - - - - - -\n室---\n线线线 题号 一 二 三 四 五 六 七 八 九 总分\n教\n---\n场---\n---\n考--- 得分\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 - -\n-\n- -\n-\n- -\n- 一、填空题(15分，每空 1.5分)\n海--- 1、当线性表的元素总数基本稳定，且很少进行插入和删除操作，但要求以最快的速度存取线性\n---\n：\n张\n- -\n-\n- -\n- -\n-\n- -\n- -\n-\n- -\n2、\n表\n对\n中\n于双\n的\n向\n元\n链\n素\n表\n时，\n,在\n应\n两\n采\n个\n用\n结\n_\n点\n__\n之\n_顺\n间\n序\n插\n_\n入\n__\n一\n存\n个\n储\n新\n结\n结\n构\n点\n。\n需 修改的指针数共 ___4___个。\n师---\n--- 3、对于一个具有 n 个结点的单链表，在给定值为 x 的结点后插入一个新结点的时间复杂度为\n教---\n课- - - - - - o（n）。\n授-\n- - 4、串是一种特殊的线性表，其特殊性表现在组成串的数据元素只能是________。\n---\n---\n订订订 5、所谓稀疏矩阵指的是_________________ _____________________。\n---\n--- 6、广义表（a,(b,c),d,e）的表尾为_________（（b，c），d，e）____________。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n7、线索二叉树中结点的左线索指向其_____前驱结点________。\n---\n--- 8、Prim（普里姆）算法适用于求________ ____的网的最小生成树；\n---\n级 :- - - - - - 9、算法的时间复杂度是_______ __________________________。\n---\n年--- 10、树的后序遍历序列与其对应的二叉树__中序____遍历序列相同。\n业\n专- -\n-\n- -\n-\n- -\n-\n二、选择题(28 分，每题 2分)\n- - - - - - 1、设计一个判别表达式中左、右括号是否配对出现的算法，采用（ d ）数据结构最佳。\n---\n--- A．线性表的顺序存储结构 B. 队列\n-\n-\n-\n-\n-\n-\n-\n-\n-\nC. 线性表的链式存储结构 D. 栈\n---\n-\n-\n-\n-\n-\n-\n名 : -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n装装装\n2、设\nA\n有\n．\n三\nXY\n个\nZ\n元 素 X， Y ，Z\nB.\n顺\nY\n序\nZX\n进 栈 （ 进 的 过\nC.\n程\nZ\n中\nXY\n允 许 出 栈\nD.\n）\nZ\n，\nY\n不\nX\n能得到的出栈序列是( c )。\n姓\n-\n-\n-\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n3 、 最 大\nA．\n容\nr\n量\nea\n为\nr=f\nn\nr\n的\non\n循\nt\n环 队 列 ， 队 尾 指\nB.\n针\n(\n是\nre\nr\na\ne\nr\na\n+\nr\n1\n，\n)\n队\nMO\n头\nD\n是\nn=f\nf\nr\nr\no\no\nn\nn\nt\nt ，则队空的条件是（ a ）。\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n- C．rear+1=front D. (rear-l) MOD n=front\n---\n-\n-\n-\n-\n-\n-\n号\n:---\n学\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第1页 共3页 ＋", "char_count": 1974}, {"page_number": 2, "text": "4、以行序为主序方式，将n阶对称矩阵A的下三角形的元素(包括主对角线上所有元素)依次存\n放于一维数组B［0..(n(n+1))/2-1］中，则在B中确定a（i<j）的位置k的关系为( d )。\nij\nA. i*(i-1)/2+j B. j*(j-1)/2+i C. i*(i+1)/2+j D. j*(j+1)/2+i\n5、深度为h的满m叉树的第k层有（ a ）个结点。(1=<k=<h)\nA．mk-1 B．mk-1 C．mh-1 D．mh-1\n6、若一棵二叉树具有9个度为2的结点，5个度为1的结点，则度为0的结点个数是（ b ）。\nA．9 B．10 C．15 D．不确定\n7、下述二叉树中,哪一种满足性质:从任一结点出发到根的路径上所经过的结点序列按其关键字\n有序（b）。\nA．二叉排序树 B．哈夫曼树 C．AVL树 D．堆\n8、 在有向图 G 的拓扑序列中，若顶点 Vi 在顶点 Vj 之前，则下列情形不可能出现的是\n（ d ）。\nA．G中有弧<Vi,Vj> B．G中有一条从Vi到Vj的路径\nC．G中没有弧<Vi,Vj> D．G中有一条从Vj到Vi的路径\n9、下面关于求关键路径的说法不正确的是（ c ）。\nA．求关键路径是以拓扑排序为基础的\nB．一个事件的最早开始时间与以该事件为尾的弧的活动最早开始时间相同\nC．一个事件的最迟开始时间为以该事件为尾的弧的活动最迟开始时间与该活动的持续\n时间之差\nD．关键活动一定位于关键路径上\n10、具有12个关键字的有序表，折半查找的平均查找长度为（ a ）。\nA. 3 B. 4 C. 2.5 D. 5\n11、下列关于m阶B-树的说法错误的是( c ) 。\nA．根结点至多有m棵子树 B．所有叶子都在同一层次上\nC. 非叶子结点至少有m/2 (m为偶数)或m/2+1（m为奇数）棵子树\nD. 根结点中的数据是有序的\n12、在下列排序算法中,哪一个算法的时间复杂度与初始序列无关（ d ）。\nA． 直接插入排序 B. 冒泡排序 C. 快速排序 D. 直接选择排序\n13、下列排序算法中，( c )排序在一趟结束后不一定能选出一个元素放在其最终位置上。\nA. 选择 B. 冒泡 C. 归并 D. 堆\n14、有一随机数组(25,84,21,46,13,27,68,35,20),采用某种方法对它们进行排序,其每趟排序\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第2页 共3页 ＋", "char_count": 1328}, {"page_number": 3, "text": "结果如下, 则该排序方法是（ b ）。\n初 始:25,84,21,46,13,27,68,35,20 第一趟:20,13,21,25,46,27,68,35,84\n第二趟:13,20,21,25,35,27,46,68,84 第三趟:13,20,21,25,27,35,46,68,84\nA．基数排序 B．快速排序 C．堆排序 D． 起泡排序\n三、（6分）已知某二叉树的后序遍历和中序遍历如下，构造出该二叉树。\n后序遍历序列： G D B E I H F C A 中序遍历序列 ：D G B A E C H I F\n由二叉树的前序遍历和后序遍历结果能否唯一确定一棵二叉树？\n四、（6分） 若叶子结点的权值分别为1,2,3,4,5,6。请构造一棵哈曼夫树，并计算该哈曼\n夫树的带权路径长度WPL。\n五、（8分）\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - （\n（\n1\n2\n）\n）\n输\n指\n入\n出\n序\n二\n列\n叉\n(\n排\n46\n序\n,8\n树\n8,\n与\n45\n平\n,3\n衡\n9,\n二\n70\n叉\n,5\n树\n8,\n的\n10\n区\n1,\n别\n10\n。\n,6 6,34)，给出构造一棵二叉排序树的步骤。\n---\n-\n-\n-\n-\n-\n-\n号 : - - - - - - 六、（10 分）设哈希函数 H(k)=K mod 7, 哈希表的地址空间为 0～6，对关键字序列\n座---\n---\n--- {32,13,49,18,22,38,21}按链地址法处理冲突的办法构造哈希表，并指出查找关键字 21\n---\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 需 要进行几次比较。\n---\n: - - - - - - 七、（12分）对下图中的带权有向图，\n号---\n室- -\n-\n-\n-\n- 1）写出其邻接表结构；\n教---\n线线线 2）根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；\n场\n考- - - - - - 3）利用Dijkstra算法，求图中顶点a到其它各顶点的最短路径，写出执行的过程。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n--- a\n---\n燕 -\n-\n- -\n-\n-\n- -\n-\n-\n- - 10 C 100\n海- - - - - - 30 e\n--- b\n张---\nD\n： - - - - - - - - - - - - B 5 0 10 60\n---\n师---\n教- - - - - - c d\n课\n授-\n-\n-\n-\n-\n- 20 G\n---\n---\n订订订\n八、（10 分）设计算法将一个带头结点的单链表 A 分解为两个带头结点的单链表 B、C，其\n--- 中B表中的结点为A表值小于0的结点，而C表中的结点为A表值大于等于0的结点（链\n- -\n-\n- -\n-\n- -\n-\n表A的元素类型为整型，要求B、C表利用A表的结点进行存储）\n---\n--- 九、（5分）\n---\n级\n:- - -\n-\n- -\n-\n- - 假设非空二叉树采用二叉链表存储，请设计一个递归算法，输出二叉树中度为2的结点数目。\n---\n年---\n业\n专- -\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第3页 共3页 ＋\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: 装装装\n名\n---\n姓---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n号\n:---\n学", "char_count": 1792}], "tables": [{"page": 1, "table_index": 0, "data": [["", "---\n- - -\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n座---\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n室---\n线线线\n教\n---\n场---\n---\n考---\n- - -\n- - -\n---\n---\n---\n- - -\n燕 - - - - - -\n海---\n---\n张---\n- - -\n- - -\n： - - - - - -\n师---\n---\n教---\n课- - - - - -\n授-\n- -\n---\n---\n订订订\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n级 :- - - - - -\n---\n年---\n业 - - -\n专- - - - - -\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n: - - -\n名 装装装\n姓---\n---\n- - -\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n:---\n号\n学", ""]]}, {"page": 1, "table_index": 1, "data": [["题号", "一", "二", "三", "四", "五", "六", "七", "八", "九", "总分"], ["得分", "", "", "", "", "", "", "", "", "", ""]]}, {"page": 3, "table_index": 0, "data": [["", "- - -\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n座---\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n:---\n号---\n室- - - -\n- -\n教---\n线线线\n场\n---\n考---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n燕 - - -\n---\n海---\n---\n张---\n- - -\n- - -\n- - -\n：---\n---\n师---\n教- - - - - -\n课---\n授- - -\n---\n---\n订订订\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n:- - - -\n级 - -\n---\n年---\n业 - - -\n专- - - -\n- -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n- - -\n: 装装装\n名\n---\n姓---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n:---\n号\n学", ""]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 3}}, "content_structure": {"filepath": "数据结构2014A.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2014A答案.pdf": {"extraction_info": {"filepath": "数据结构2014A答案.pdf", "method": "pdfplumber", "text": "2014 学年第一学期数据结构（A）卷试题答案\n一、 填空题（15分，每空1.5分）\n1、顺序\n2、4\n3、O(n)\n4、字符\n5、矩阵中非零元的个数与总元素的个数之比小于0.05\n6、（（b,c）,d,e）\n7、前驱结点\n8、边稠密的网\n9、算法中基本操作重复执行的次数\n10、中序遍历\n二、选择题(28 分，每题2分)\n1、D 2、C 3、A 4、D 5、A 6、B 7、B\n8、D 9、C 10、A 11、C 12、D 13、C 14、B\n三、（6分）\n(1)构造的二叉树如右图：\nA\nB\nB C\nA\nD E F\nA\nG H\nG\nG\nI\n（2）不能唯一确定\nG\nG\n四、（6分）所构造的哈夫曼树为： 21\n12 9\n6 6 4 5\n3 3\n1 2\n带权路径长度WPL\n=3*3+（1+2）*4+2*6+（4+5）*2=51\n五、（8分）\n（1） 构造的二叉排序树：\n46\n45 88\n39 70 101\n10 58\n66\n34\n（2）平衡二叉树是二叉排序树，而且它的左右子树都是平衡二叉树且左右子树的深度之\n差绝对值不超过1.\n六、（10分）\n查找关键字21需要比较2次。\n0 49 21\n1 22\n2\n3 38\n4 32 18\n5\n6 13\n七、（12分）\n（1）该有向图的邻接表为\n0\na 1 10 3 30 4 100\nb 2 50\n1\n2\nc 4 10\nd 2 20\n3\n4\ne 3 60\n步骤\ni=1 i=2 i=3 i=4\n节点\n{a,b}\nb\n10\n{a,b,c} {a,d,c}\nc ∞\n60 50\n{a,d} {a,d}\nd\n30 30\n{a,e} {a,e} {a,e} {a,d,c,e}\ne\n100 100 100 60\n选取关键\nb d c e\n路径点\nS (b) (b,d) (b,c,d) (b,c,d,e)\n八、（10分）\n算法编写如下：\nVoid split(Linklist ha, Linklist &hb,Linklist &hc)\n{Listnode *p=ha->next,*rb,*rc;\n∥链结点结构是（data，next），data是数据域，next是链域。\nhb=ha; rb=hb;\nhc=(Linklist)malloc(sizeof(linknode));\nrc=hc;\nwhile(p!=NULL)\n{ if(p->data<0)\n{rb->next=p; rb=p; p=p->next;}\nElse { rc->next=p; rc=p; p=p->next;}\n}\nrb->next=rc->next=NULL;\n}\n九、（6分）\n算法编写如下：\nInt count(BinTreeNode * T)\n{if (T==NULL) retur n 0;\nelse if (T->lchild!=null && T->rchild!=null)\nreturn (1+count(T->lchild)+ count(T->rchild));\nelse return (count(T->lchild)+ count(T->rchild));", "pages": [{"page_number": 1, "text": "2014 学年第一学期数据结构（A）卷试题答案\n一、 填空题（15分，每空1.5分）\n1、顺序\n2、4\n3、O(n)\n4、字符\n5、矩阵中非零元的个数与总元素的个数之比小于0.05\n6、（（b,c）,d,e）\n7、前驱结点\n8、边稠密的网\n9、算法中基本操作重复执行的次数\n10、中序遍历\n二、选择题(28 分，每题2分)\n1、D 2、C 3、A 4、D 5、A 6、B 7、B\n8、D 9、C 10、A 11、C 12、D 13、C 14、B\n三、（6分）\n(1)构造的二叉树如右图：\nA\nB\nB C\nA\nD E F\nA\nG H\nG\nG\nI\n（2）不能唯一确定\nG\nG\n四、（6分）所构造的哈夫曼树为： 21\n12 9\n6 6 4 5\n3 3\n1 2", "char_count": 326}, {"page_number": 2, "text": "带权路径长度WPL\n=3*3+（1+2）*4+2*6+（4+5）*2=51\n五、（8分）\n（1） 构造的二叉排序树：\n46\n45 88\n39 70 101\n10 58\n66\n34\n（2）平衡二叉树是二叉排序树，而且它的左右子树都是平衡二叉树且左右子树的深度之\n差绝对值不超过1.\n六、（10分）\n查找关键字21需要比较2次。\n0 49 21\n1 22\n2\n3 38\n4 32 18\n5\n6 13", "char_count": 197}, {"page_number": 3, "text": "七、（12分）\n（1）该有向图的邻接表为\n0\na 1 10 3 30 4 100\nb 2 50\n1\n2\nc 4 10\nd 2 20\n3\n4\ne 3 60\n步骤\ni=1 i=2 i=3 i=4\n节点\n{a,b}\nb\n10\n{a,b,c} {a,d,c}\nc ∞\n60 50\n{a,d} {a,d}\nd\n30 30\n{a,e} {a,e} {a,e} {a,d,c,e}\ne\n100 100 100 60\n选取关键\nb d c e\n路径点\nS (b) (b,d) (b,c,d) (b,c,d,e)\n八、（10分）\n算法编写如下：\nVoid split(Linklist ha, Linklist &hb,Linklist &hc)\n{Listnode *p=ha->next,*rb,*rc;\n∥链结点结构是（data，next），data是数据域，next是链域。\nhb=ha; rb=hb;\nhc=(Linklist)malloc(sizeof(linknode));\nrc=hc;\nwhile(p!=NULL)\n{ if(p->data<0)\n{rb->next=p; rb=p; p=p->next;}\nElse { rc->next=p; rc=p; p=p->next;}\n}\nrb->next=rc->next=NULL;\n}", "char_count": 569}, {"page_number": 4, "text": "九、（6分）\n算法编写如下：\nInt count(BinTreeNode * T)\n{if (T==NULL) retur n 0;\nelse if (T->lchild!=null && T->rchild!=null)\nreturn (1+count(T->lchild)+ count(T->rchild));\nelse return (count(T->lchild)+ count(T->rchild));", "char_count": 208}], "tables": [{"page": 1, "table_index": 0, "data": [["", "", ""]]}, {"page": 2, "table_index": 0, "data": [["", "", ""]]}, {"page": 2, "table_index": 1, "data": [[""], [""], [""], [""], [""], [""], [""]]}, {"page": 2, "table_index": 2, "data": [["49", ""]]}, {"page": 2, "table_index": 3, "data": [["21", ""]]}, {"page": 2, "table_index": 4, "data": [["22", ""]]}, {"page": 2, "table_index": 5, "data": [["38", ""]]}, {"page": 2, "table_index": 6, "data": [["32", ""]]}, {"page": 2, "table_index": 7, "data": [["18", ""]]}, {"page": 2, "table_index": 8, "data": [["13", ""]]}, {"page": 3, "table_index": 0, "data": [[null, "a", ""], ["0\n1\n2\n3\n4", null, null], [null, "b", ""], [null, "c", ""], [null, "d", ""], [null, "e", ""]]}, {"page": 3, "table_index": 1, "data": [["1", "10", ""]]}, {"page": 3, "table_index": 2, "data": [["3", "30"]]}, {"page": 3, "table_index": 3, "data": [["4", "100", "", ""]]}, {"page": 3, "table_index": 4, "data": [["2", "50", ""]]}, {"page": 3, "table_index": 5, "data": [["4", "10", ""]]}, {"page": 3, "table_index": 6, "data": [["2", "20", ""]]}, {"page": 3, "table_index": 7, "data": [["3", "60", ""]]}, {"page": 3, "table_index": 8, "data": [["步骤\n节点", "i=1", "i=2", "i=3", "i=4"], ["b", "{a,b}\n10", "", "", ""], ["c", "∞", "{a,b,c}\n60", "{a,d,c}\n50", ""], ["d", "{a,d}\n30", "{a,d}\n30", "", ""], ["e", "{a,e}\n100", "{a,e}\n100", "{a,e}\n100", "{a,d,c,e}\n60"], ["选取关键\n路径点", "b", "d", "c", "e"], ["S", "(b)", "(b,d)", "(b,c,d)", "(b,c,d,e)"]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 4}}, "content_structure": {"filepath": "数据结构2014A答案.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2015A.pdf": {"extraction_info": {"filepath": "数据结构2015A.pdf", "method": "pdfplumber", "text": "中国海洋大学全日制本科课程期末考试试卷\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n--- 2015年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n---\n号 : - - - - - -\n座--- 试卷类型： A 卷 命题人: 审核人：________\n---\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n-\n-\n-\n- ______________。\n---\n号 : - - - - - -\n室---\n线线线 题号 一 二 三 四 五 六 七 八 九 总分\n教\n---\n场---\n---\n考--- 得分\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 - -\n-\n- -\n-\n- -\n- 一、填空题(24 分，每空 2分)\n海--- 1、在一个长度为n的顺序表中，删除第i个元素（0<=i<=n-1）时，需向前移动________个元素。\n---\n：\n张\n- -\n-\n- -\n- -\n-\n- -\n- -\n-\n- -\n2、\n删\n假\n除\n定\n操\n一\n作\n棵\n的\n二\n时\n叉\n间\n树\n复\n的\n杂\n结\n度\n点数\n为\n为\n___\n3\n_\n3\n_\n个\n__\n，\n_。\n则 它的最小高度为_________。\n师---\n--- 3、n阶对称矩阵a满足a[i][j]=a[j][i],i,j=0..n-1,，用一维数组m压缩存储时，m的长度为\n教---\n课- - - - - - ______。\n授-\n- - 4、两个字符串相等的充分必要条件是_____________________。\n---\n---\n订订订 5、有n个顶点的有向强连通图最少有_______条弧。\n---\n--- 6 、 设 有 向 图 G 的 顶 点 集 合 为 {v1,v2,v3,v4,v5}, 弧 的 集 合 为 ，\n-\n-\n-\n-\n-\n-\n-\n-\n-\n{<v1,v2>,<v2,v4>,<v3,v5>,<v1,v3.>,<v1,v5>,<v2,v3>,<v3,v4>,<v4,v5>},入度最大的顶\n---\n--- 点是________，G的拓扑序列是________________（任写一种）。\n---\n级 :- - - - - - 7、对于关键字依次为{8,32,46,49,52,68,70,75,88,90,92,95，100}的查找表，采用二分法查找，\n---\n年--- 查找关键字为75的记录时，经过________次关键字的比较后才成功。\n业\n专- -\n-\n- -\n-\n- -\n-\n8、32阶B-树中，除根结点和叶子结点外，每个结点至少包含_______个关键字。\n- - - - - - 9、图的广度优先遍历算法用到一个队列，每个顶点最多入队_______次。\n---\n--- 10、在AOE网中，关键路径是指______________________________。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n-\n-\n-\n-\n-\n- 二、选择题(16 分，每题 2分)\n名 : -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n装装装\n1、递\nA.\n归\nf(\n模\n1\n型\n)=0\n为 ： f ( 1\nB\n)\n.\n=\nf\n1\n(\n,\n1\nf(\n)\nn\n=\n)\n1\n= f ( n - 1\nC\n)\n.\n+\nf\nn\n(\n,\n0\n(\n)\n其\n=\n中\n1\nn > 1 )\nD\n,\n.\n其\nf(\n递\nn)\n归\n=n\n出 口 是（ ）。\n姓\n-\n-\n-\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n2、在\nA．\n单\np\n链\n->\n表\nne\n中\nxt\n指\n=s\n针\n;s\n为\n->\np\nne\n的\nxt\n结\n=p\n点\n->\n之\nne\n后\nxt\n，\n;\n插 入\nB．\n指 针\ns\n为\n->\ns\nne\n的\nxt\n结\n=p\n点\n->\n，\nne\n正\nxt\n确\n;p\n的\n->\n操\nne\n作\nxt\n是\n=s\n（\n;\n）。\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n- C．p->next=s;p->next=s->next; D． p->next=s->next;p->next=s;\n---\n-\n-\n-\n-\n-\n-\n号\n:---\n学\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第1页 共3页 ＋\n3、以下错误的是（ ）\n(1) 静态链表既有顺序存储的优点，又有动态链表的优点。所以，它存取表中第i个元素的\n时间与i无关。\n(2) 静态链表中能容纳的元素个数的最大数在表定义时就确定了，以后不能增加。\n(3) 静态链表与动态链表在元素的插入、删除操作上类似，不需要做元素的移动。\nA．1,2 B．1 C．1,2,3 D.2\n4、设一个栈的输入序列是 1，2，3，4，5,则下列序列中，（ ）是栈的合法输出序列。\nA. 5 1 2 3 4 B. 4 5 1 3 2 C. 4 3 1 2 5 D. 3 2 1 5 4\n5、循环队列存储在数组A[0..m]中，则入队时尾指针的操作为（ ）。\nA. rear=rear+1 B. rear=(rear+1)mod(m-1)\nC. rear=(rear+1) mod m D. rear=(rear+1)mod(m+1)\n6、下面说法不正确的是( )。\nA. 广义表的表头总是一个广义表 B. 广义表的表尾总是一个广义表\nC. 广义表难以用顺序存储结构 D. 广义表可以是一个多层次的结\n构\n7、对具有n个关键字的哈希表进行查找，平均查找长度是（ ）\nA.O(1) B.O(log2(n)) C.O(n) D.与n无关\n8、在文件“局部有序”或文件长度较小的情况下，最佳内部排序的方法是（ ）\nA.直接插入排序 B.冒泡排序 C.简单选择排序 D.快速排序\n四、（10分）若已知一棵二叉树的前序序列是BEFCGDH，中序列是FEBGCHD，请画出这棵树及其\n后序线索树。假设上述二叉树是由某森林转换而成的，请画出森林。\n五、（6分）若叶子结点的权值分别为{4,5,6,7,10,12,18}。请构造一棵哈夫曼树，并计算该\n哈夫曼树的带权路径长度WPL。\n六、（12分）\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 1、从\n二\n空\n叉\n二\n排\n叉\n序\n树\n树\n开\n的\n始\n步\n，\n骤\n逐\n，\n个\n并\n插\n画\n入\n出\n关\n删\n键\n除\n字\n关\n序\n键\n列\n字\n{\n7\n4\n3\n1，\n后\n1\n的\n8,\n二\n73\n叉\n,1\n树\n0,\n。\n5, 68,99,27，45 }，给出构造一棵\n---\n- - - - - - 2、什么是哈希表，请简述哈希表的查找过程（也可以用流程图表示）\n号 : - - - - - -\n座---\n---\n--- 七、（10分）\n---\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 1、\n行\n已\n从\n知\n小\n一\n到\n序\n大\n列\n的\n的\n排\n关\n序\n键\n，\n码\n选\n为\n择\n{3\n序\n0,\n列\n45\n中\n,10\n3\n,\n0\n4\n为\n,\n轴\n6,\n值\n10\n，\n0,\n写\n45\n出\n*，\n第\n7\n一\n5,\n趟\n8}\n排\n。\n序\n用\n的\n快\n结\n速\n果\n排\n。\n序 方法对此序列进\n---\n: -\n-\n-\n-\n-\n-\n号---\n室- -\n-\n-\n-\n-\n教---\n线线线 2、关键码序列（tang,deng,an,wan,shi,bai,fang,liu）按字典顺序排序（“an”<“bai”，\n场\n考-\n-\n-\n-\n-\n- ＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第2页 共3页 ＋\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 -\n-\n-\n-\n-\n-\n-\n-\n-\n---\n海---\n---\n张---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n：---\n---\n师---\n教- - - - - -\n课---\n授- - -\n---\n---\n订订订\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n级\n:- -\n-\n-\n-\n-\n-\n-\n-\n---\n年---\n业\n专- -\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: 装装装\n名\n---\n姓---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n号\n:---\n学\n空格小于任何其它字符）。采用“右补空格”，使得这些关键码都成为4位字符的等长关\n键码。按最低位优先法进行基数排序。请写出前二趟分配和收集后得到的序列情况。\n八、（12分）对下列带权有向图，\n1）写出其邻接表结构；\n2）根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；\n3）利用Dijkstra算法，求图中顶点a到其它各顶点的最短路径，写出执行的过程。\n11 6\na d e\nC D G\n7 10 5 9 2\nf\nb c\n9 G\nB\n九、（10 分）已知一个带头结点的单链表中每个结点存放一个整数，并且结点数不少于 2。\n请设计一算法，判断该链表中第二项起的每个元素值是否等于其序号的平方减去其前驱结点\n的值，若满足则返回ture，否则返回false.\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第3页 共3页 ＋", "pages": [{"page_number": 1, "text": "中国海洋大学全日制本科课程期末考试试卷\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n--- 2015年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n---\n号 : - - - - - -\n座--- 试卷类型： A 卷 命题人: 审核人：________\n---\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n-\n-\n-\n- ______________。\n---\n号 : - - - - - -\n室---\n线线线 题号 一 二 三 四 五 六 七 八 九 总分\n教\n---\n场---\n---\n考--- 得分\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 - -\n-\n- -\n-\n- -\n- 一、填空题(24 分，每空 2分)\n海--- 1、在一个长度为n的顺序表中，删除第i个元素（0<=i<=n-1）时，需向前移动________个元素。\n---\n：\n张\n- -\n-\n- -\n- -\n-\n- -\n- -\n-\n- -\n2、\n删\n假\n除\n定\n操\n一\n作\n棵\n的\n二\n时\n叉\n间\n树\n复\n的\n杂\n结\n度\n点数\n为\n为\n___\n3\n_\n3\n_\n个\n__\n，\n_。\n则 它的最小高度为_________。\n师---\n--- 3、n阶对称矩阵a满足a[i][j]=a[j][i],i,j=0..n-1,，用一维数组m压缩存储时，m的长度为\n教---\n课- - - - - - ______。\n授-\n- - 4、两个字符串相等的充分必要条件是_____________________。\n---\n---\n订订订 5、有n个顶点的有向强连通图最少有_______条弧。\n---\n--- 6 、 设 有 向 图 G 的 顶 点 集 合 为 {v1,v2,v3,v4,v5}, 弧 的 集 合 为 ，\n-\n-\n-\n-\n-\n-\n-\n-\n-\n{<v1,v2>,<v2,v4>,<v3,v5>,<v1,v3.>,<v1,v5>,<v2,v3>,<v3,v4>,<v4,v5>},入度最大的顶\n---\n--- 点是________，G的拓扑序列是________________（任写一种）。\n---\n级 :- - - - - - 7、对于关键字依次为{8,32,46,49,52,68,70,75,88,90,92,95，100}的查找表，采用二分法查找，\n---\n年--- 查找关键字为75的记录时，经过________次关键字的比较后才成功。\n业\n专- -\n-\n- -\n-\n- -\n-\n8、32阶B-树中，除根结点和叶子结点外，每个结点至少包含_______个关键字。\n- - - - - - 9、图的广度优先遍历算法用到一个队列，每个顶点最多入队_______次。\n---\n--- 10、在AOE网中，关键路径是指______________________________。\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n-\n-\n-\n-\n-\n- 二、选择题(16 分，每题 2分)\n名 : -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n装装装\n1、递\nA.\n归\nf(\n模\n1\n型\n)=0\n为 ： f ( 1\nB\n)\n.\n=\nf\n1\n(\n,\n1\nf(\n)\nn\n=\n)\n1\n= f ( n - 1\nC\n)\n.\n+\nf\nn\n(\n,\n0\n(\n)\n其\n=\n中\n1\nn > 1 )\nD\n,\n.\n其\nf(\n递\nn)\n归\n=n\n出 口 是（ ）。\n姓\n-\n-\n-\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n- -\n-\n-\n-\n2、在\nA．\n单\np\n链\n->\n表\nne\n中\nxt\n指\n=s\n针\n;s\n为\n->\np\nne\n的\nxt\n结\n=p\n点\n->\n之\nne\n后\nxt\n，\n;\n插 入\nB．\n指 针\ns\n为\n->\ns\nne\n的\nxt\n结\n=p\n点\n->\n，\nne\n正\nxt\n确\n;p\n的\n->\n操\nne\n作\nxt\n是\n=s\n（\n;\n）。\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n- C．p->next=s;p->next=s->next; D． p->next=s->next;p->next=s;\n---\n-\n-\n-\n-\n-\n-\n号\n:---\n学\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第1页 共3页 ＋", "char_count": 2139}, {"page_number": 2, "text": "3、以下错误的是（ ）\n(1) 静态链表既有顺序存储的优点，又有动态链表的优点。所以，它存取表中第i个元素的\n时间与i无关。\n(2) 静态链表中能容纳的元素个数的最大数在表定义时就确定了，以后不能增加。\n(3) 静态链表与动态链表在元素的插入、删除操作上类似，不需要做元素的移动。\nA．1,2 B．1 C．1,2,3 D.2\n4、设一个栈的输入序列是 1，2，3，4，5,则下列序列中，（ ）是栈的合法输出序列。\nA. 5 1 2 3 4 B. 4 5 1 3 2 C. 4 3 1 2 5 D. 3 2 1 5 4\n5、循环队列存储在数组A[0..m]中，则入队时尾指针的操作为（ ）。\nA. rear=rear+1 B. rear=(rear+1)mod(m-1)\nC. rear=(rear+1) mod m D. rear=(rear+1)mod(m+1)\n6、下面说法不正确的是( )。\nA. 广义表的表头总是一个广义表 B. 广义表的表尾总是一个广义表\nC. 广义表难以用顺序存储结构 D. 广义表可以是一个多层次的结\n构\n7、对具有n个关键字的哈希表进行查找，平均查找长度是（ ）\nA.O(1) B.O(log2(n)) C.O(n) D.与n无关\n8、在文件“局部有序”或文件长度较小的情况下，最佳内部排序的方法是（ ）\nA.直接插入排序 B.冒泡排序 C.简单选择排序 D.快速排序\n四、（10分）若已知一棵二叉树的前序序列是BEFCGDH，中序列是FEBGCHD，请画出这棵树及其\n后序线索树。假设上述二叉树是由某森林转换而成的，请画出森林。\n五、（6分）若叶子结点的权值分别为{4,5,6,7,10,12,18}。请构造一棵哈夫曼树，并计算该\n哈夫曼树的带权路径长度WPL。\n六、（12分）\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 1、从\n二\n空\n叉\n二\n排\n叉\n序\n树\n树\n开\n的\n始\n步\n，\n骤\n逐\n，\n个\n并\n插\n画\n入\n出\n关\n删\n键\n除\n字\n关\n序\n键\n列\n字\n{\n7\n4\n3\n1，\n后\n1\n的\n8,\n二\n73\n叉\n,1\n树\n0,\n。\n5, 68,99,27，45 }，给出构造一棵\n---\n- - - - - - 2、什么是哈希表，请简述哈希表的查找过程（也可以用流程图表示）\n号 : - - - - - -\n座---\n---\n--- 七、（10分）\n---\n-\n-\n- -\n-\n-\n- -\n-\n-\n- - 1、\n行\n已\n从\n知\n小\n一\n到\n序\n大\n列\n的\n的\n排\n关\n序\n键\n，\n码\n选\n为\n择\n{3\n序\n0,\n列\n45\n中\n,10\n3\n,\n0\n4\n为\n,\n轴\n6,\n值\n10\n，\n0,\n写\n45\n出\n*，\n第\n7\n一\n5,\n趟\n8}\n排\n。\n序\n用\n的\n快\n结\n速\n果\n排\n。\n序 方法对此序列进\n---\n: -\n-\n-\n-\n-\n-\n号---\n室- -\n-\n-\n-\n-\n教---\n线线线 2、关键码序列（tang,deng,an,wan,shi,bai,fang,liu）按字典顺序排序（“an”<“bai”，\n场\n考-\n-\n-\n-\n-\n- ＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第2页 共3页 ＋\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n燕 -\n-\n-\n-\n-\n-\n-\n-\n-\n---\n海---\n---\n张---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n：---\n---\n师---\n教- - - - - -\n课---\n授- - -\n---\n---\n订订订\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n级\n:- -\n-\n-\n-\n-\n-\n-\n-\n---\n年---\n业\n专- -\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: 装装装\n名\n---\n姓---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n-\n-\n-\n-\n-\n-\n-\n-\n-\n---\n---\n---\n号\n:---\n学", "char_count": 2019}, {"page_number": 3, "text": "空格小于任何其它字符）。采用“右补空格”，使得这些关键码都成为4位字符的等长关\n键码。按最低位优先法进行基数排序。请写出前二趟分配和收集后得到的序列情况。\n八、（12分）对下列带权有向图，\n1）写出其邻接表结构；\n2）根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；\n3）利用Dijkstra算法，求图中顶点a到其它各顶点的最短路径，写出执行的过程。\n11 6\na d e\nC D G\n7 10 5 9 2\nf\nb c\n9 G\nB\n九、（10 分）已知一个带头结点的单链表中每个结点存放一个整数，并且结点数不少于 2。\n请设计一算法，判断该链表中第二项起的每个元素值是否等于其序号的平方减去其前驱结点\n的值，若满足则返回ture，否则返回false.\n＋------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ 第3页 共3页 ＋", "char_count": 681}], "tables": [{"page": 1, "table_index": 0, "data": [["", "---\n- - -\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n座---\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n室---\n线线线\n教\n---\n场---\n---\n考---\n- - -\n- - -\n---\n---\n---\n- - -\n燕 - - - - - -\n海---\n---\n张---\n- - -\n- - -\n： - - - - - -\n师---\n---\n教---\n课- - - - - -\n授-\n- -\n---\n---\n订订订\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n级 :- - - - - -\n---\n年---\n业 - - -\n专- - - - - -\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n: - - -\n名 装装装\n姓---\n---\n- - -\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n:---\n号\n学", ""]]}, {"page": 1, "table_index": 1, "data": [["题号", "一", "二", "三", "四", "五", "六", "七", "八", "九", "总分"], ["得分", "", "", "", "", "", "", "", "", "", ""]]}, {"page": 2, "table_index": 0, "data": [["", "- - -\n- - -\n- - -\n- - -\n---\n---\n---\n号 : - - - - - -\n座---\n---\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n:---\n号---\n室- - - -\n- -\n教---\n线线线\n场\n---\n考---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n燕 - - -\n---\n海---\n---\n张---\n- - -\n- - -\n- - -\n：---\n---\n师---\n教- - - - - -\n课---\n授- - -\n---\n---\n订订订\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n:- - - -\n级 - -\n---\n年---\n业 - - -\n专- - - -\n- -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n- - -\n: 装装装\n名\n---\n姓---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n- - -\n- - -\n- - -\n---\n---\n---\n:---\n号\n学", ""]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 3}}, "content_structure": {"filepath": "数据结构2015A.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2016A.pdf": {"extraction_info": {"filepath": "数据结构2016A.pdf", "method": "pdfplumber", "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n: -\n号 - 2016年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n座 -\n-\n- 试卷类型： A 卷 命题人: 审核人：________\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_4_页，除考场规定的必需用品外还可携带的文具有\n号 : - - ______________。\n-\n室 -\n- 题号 一 二 三 四 五 六 七 八 九 总分\n教 -\n场 -\n-\n考 线 得分\n-\n-\n-\n-\n-\n- 一、填空题(共 10 题，每题 2 分，共 20 分)\n-\n燕 -\n- 1.设一棵三叉树中有50个度数为0的结点，21个度数为2的结点，则该二叉树中度数为3的结\n海 -\n张 - 点数目有________。\n-\n-\n- 2.设一棵二叉树的前序序列为ABC，则有________种不同的二叉树可以得到这种序列。\n-\n： -\n师 - 3.广义表（（a），a）的表头是 ________。\n-\n教 - 4.已知一算术表达式的中缀形式为A＋B *C–D/E，后缀形式为___________。\n-\n课 -\n授 - 5.稀疏矩阵一般的压缩存储方式有________和十字链表。\n-\n- 6.队列是一种________的线性表。\n-\n- 7.折半查找适用于________存储的有序表。\n-\n订\n8.在一棵高为2 的5阶B 树中，所含关键字的个数最少是________。\n- -\n-\n- 9.设有向图G中有向边的集合E={<1，2>，<2，3>，<1，4>，<4，2>，<4，3>}，则该图的一种\n-\n- 拓扑序列为_________。\n-\n-\n级 : - 10.在快速排序、堆排序、归并排序中，_________排序是稳定的。\n-\n年 -\n-\n业 -\n专 - 二、选择题(共 10 题，每题 2 分，共 20 分)\n-\n- 1.与单链表相比，双链表的优点之一是（ ）。\n-\n-\n- A.插入、删除操作更简单 B.可以进行随机访问\n-\n- C.可以省略表头指针或表尾指针 D.顺序访问相邻结点更灵活\n-\n-\n-\n-\n-\n-\n2. 在存储数据时，通常不仅要存储各数据元素的值，还要存储（ ）。\n-\n: - A.数据的处理方法 B.数据元素的类型 C.数据元素之间的关系 D.数据的存储方法\n名 -\n装\n姓\n-\n-\n-\n3.在n个结点的线性表的数组实现中，算法的时间复杂度是O（1）的操作是（ ）。\n-\n- A.访问第i（1＜＝i＜＝n）个结点和求第i个结点的直接前驱（1＜i＜＝n）\n-\n-\n- B.在第i（1＜＝i＜＝n）个结点后插入一个新结点\n-\n- C.删除第i（1＜＝i＜＝n）个结点\n-\n-\n- D.以上都不对\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共4页 ＋\n4.设栈S和队列Q的初始状态均为空，元素abcdefg依次进入栈S。若每个元素出栈后立即进入\n队列Q，且7个元素出队的顺序是bdcfeag，则栈S的容量至少是（ ）。\nA.1 B.2 C.3 D.4\n5.在线索化树中，每个结点必须设置一个标志来说明它的左、右链指向的是树结构信息，还是线\n索化信息，若0标识树结构信息，1标识线索，对应叶结点的左、右链域应标识为（ ）。\nA. 0 、0 B.0、 1 C.1、 0 D.1、 1\n6. 下列关于最小生成树的说法中，正确的是（ ）。\nⅠ、最小生成树的代价唯一\nⅡ、所有权值最小的边一定会出现在所有的最小生成树中\nⅢ、使用普里姆（Prim）算法从不同顶点开始得到的最小生成树一定相同\nⅣ、使用普里姆算法和克鲁斯卡尔算法得到的最小生成树总不相同\nA．Ⅰ B.Ⅱ C.Ⅰ和Ⅲ D.Ⅱ和Ⅳ\n7.下列哪一种图的邻接矩阵是对称矩阵？（ ）\nA．有向图 B．无向图 C．AOV网 D．AOE网\n8. 对具有n个节点、e条边且使用邻接表存储的有向图进行广度优先遍历，其算法时间复杂度（ ）。\nA.O(n) B.O(e) C.O(n+e) D.O(n*e)\n9.在所有的排序方法中，关键字比较的次数与记录的初始排列次序无关的是（ ）。\nA.希尔排序 B.冒泡排序 C.直接插入排序 D.直接选择排序\n10.对一组数据（2，12，16，88，5，10）进行排序，若前三趟排序结果如下,则采用的排序方法\n可能是（ ）。\n第一趟：2，12，16，5，10，88\n第二趟：2，12，5，10，16，88\n第三趟：2，5，10，12，16，88\nA. 希尔排序 B. 起泡排序 C.归并排序 D.基数排序\n三、简答题(共 7分)\n1. 写出下列二叉树的中序遍历序列，并将其改为中序线索二叉树。\n2. 请将这棵二叉树转换成对应的森林 。\nA\nA\nB C\nD E F\n＋------------------------------------------------------------------------------- ----------- 第2页 共4页 ＋\n四、简答题(共 6 分)\n给定字母 a,b,c,d,e 的使用频率为 0.09, 0.17, 0.2, 0.23, 0.31。设计以该权值为基础的哈夫曼树，\n以及哈夫曼编码，并求其带权路径长度。\n-\n-\n:\n-\n-\n号 -\n- 五、简答题(共 3题，每题 5分，共 15 分)\n座 -\n-\n- 1. 输入关键字序列{12, 4, 1, 7, 8, 10}，给出构造一棵平衡二叉树的步骤。\n-\n- 2.序列的关键码为{46, 32, 55, 81, 65, 11, 25, 43}。 ，写出第一趟快速排序的结果。\n-\n-\n- 3.序列的关键码为{80,70,33,65,24,56,48}，请用筛选法建立最小堆。\n-\n-\n: -\n号 -\n- 六、简答题(共 6 分)\n室 -\n-\n教 - 设 散 列 表 的 长 度 为 13 ， 散 列 函 数 为 H(K)=K MOD 13 ， 给 定 的 关 键 字 序 列 为 ：\n场 -\n- 19，14，23，01，68，20，84。试画出用线性探测再散列解决冲突 时所构成的散列表，并求\n考 线\n- 等概率情况下查找成功时的平均查找长 度。\n-\n-\n-\n-\n- 七、简答题(共 12 分)\n-\n-\n- 对下列带权有向图，\n-\n-\n-\n1）写出其邻接表结构；\n-\n- 2）根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列；\n-\n： -\n师 - 3）利用Dijkstra算法，求图中顶点A到其它各顶点的最短路径，写出执行的过程。\n-\n教 -\n-\n课 -\n授 -\n- -\nA\n6\nB\n6\nC\n-\n-\n- C D G\n订\n- - 8 10 6 10 2\n-\n-\n-\n-\n-\n级 : - D E F\n年 - -\n- B\n2 5 G\n业 -\n专 -\n-\n- 八、简答题(共 10分)\n-\n-\n- 请写一算法，将带有头结点的非空单链表中数据域值最小的那个结点移到链表的最前面。\n-\n- 要求：不得额外申请新的链结点。\n-\n-\n-\n-\n-\n-\n九、简答题(共 4分)\n-\n: -\n名 -\n装\n姓 下面给出的算法功能是先序建立二叉树，请在下划线处填上正确的内容。\n-\n-\n- typedef struct node {\n-\n- char data；\n-\n-\n- struct node *leftchild, *rightchild；\n-\n- } BinTreeNode;\n-\n-\n-\n-\n-\n号 : - BinTreeNode *Crt_BinTree( )\n学 ＋------------------------------------------------------------------------------- ----------- 第3页 共4页 ＋\n{ char ch; BinTreeNode *t；\nprintf(\"please input data:\\n\")；\nscanf (\"%c\",&ch)；\nif (ch=='#') ___________ ; //输入#，表示空树\nelse {\nt=(BinTreeNode *) malloc(sizeof(BinTreeNode))；\nt->data = ch；\n______________________；\nt->rightchild=Crt_BinTree( )；\n}\nreturn (t)；\n}\n＋------------------------------------------------------------------------------- ----------- 第4页 共4页 ＋", "pages": [{"page_number": 1, "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n: -\n号 - 2016年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n座 -\n-\n- 试卷类型： A 卷 命题人: 审核人：________\n-\n-\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_4_页，除考场规定的必需用品外还可携带的文具有\n号 : - - ______________。\n-\n室 -\n- 题号 一 二 三 四 五 六 七 八 九 总分\n教 -\n场 -\n-\n考 线 得分\n-\n-\n-\n-\n-\n- 一、填空题(共 10 题，每题 2 分，共 20 分)\n-\n燕 -\n- 1.设一棵三叉树中有50个度数为0的结点，21个度数为2的结点，则该二叉树中度数为3的结\n海 -\n张 - 点数目有________。\n-\n-\n- 2.设一棵二叉树的前序序列为ABC，则有________种不同的二叉树可以得到这种序列。\n-\n： -\n师 - 3.广义表（（a），a）的表头是 ________。\n-\n教 - 4.已知一算术表达式的中缀形式为A＋B *C–D/E，后缀形式为___________。\n-\n课 -\n授 - 5.稀疏矩阵一般的压缩存储方式有________和十字链表。\n-\n- 6.队列是一种________的线性表。\n-\n- 7.折半查找适用于________存储的有序表。\n-\n订\n8.在一棵高为2 的5阶B 树中，所含关键字的个数最少是________。\n- -\n-\n- 9.设有向图G中有向边的集合E={<1，2>，<2，3>，<1，4>，<4，2>，<4，3>}，则该图的一种\n-\n- 拓扑序列为_________。\n-\n-\n级 : - 10.在快速排序、堆排序、归并排序中，_________排序是稳定的。\n-\n年 -\n-\n业 -\n专 - 二、选择题(共 10 题，每题 2 分，共 20 分)\n-\n- 1.与单链表相比，双链表的优点之一是（ ）。\n-\n-\n- A.插入、删除操作更简单 B.可以进行随机访问\n-\n- C.可以省略表头指针或表尾指针 D.顺序访问相邻结点更灵活\n-\n-\n-\n-\n-\n-\n2. 在存储数据时，通常不仅要存储各数据元素的值，还要存储（ ）。\n-\n: - A.数据的处理方法 B.数据元素的类型 C.数据元素之间的关系 D.数据的存储方法\n名 -\n装\n姓\n-\n-\n-\n3.在n个结点的线性表的数组实现中，算法的时间复杂度是O（1）的操作是（ ）。\n-\n- A.访问第i（1＜＝i＜＝n）个结点和求第i个结点的直接前驱（1＜i＜＝n）\n-\n-\n- B.在第i（1＜＝i＜＝n）个结点后插入一个新结点\n-\n- C.删除第i（1＜＝i＜＝n）个结点\n-\n-\n- D.以上都不对\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共4页 ＋", "char_count": 1265}, {"page_number": 2, "text": "4.设栈S和队列Q的初始状态均为空，元素abcdefg依次进入栈S。若每个元素出栈后立即进入\n队列Q，且7个元素出队的顺序是bdcfeag，则栈S的容量至少是（ ）。\nA.1 B.2 C.3 D.4\n5.在线索化树中，每个结点必须设置一个标志来说明它的左、右链指向的是树结构信息，还是线\n索化信息，若0标识树结构信息，1标识线索，对应叶结点的左、右链域应标识为（ ）。\nA. 0 、0 B.0、 1 C.1、 0 D.1、 1\n6. 下列关于最小生成树的说法中，正确的是（ ）。\nⅠ、最小生成树的代价唯一\nⅡ、所有权值最小的边一定会出现在所有的最小生成树中\nⅢ、使用普里姆（Prim）算法从不同顶点开始得到的最小生成树一定相同\nⅣ、使用普里姆算法和克鲁斯卡尔算法得到的最小生成树总不相同\nA．Ⅰ B.Ⅱ C.Ⅰ和Ⅲ D.Ⅱ和Ⅳ\n7.下列哪一种图的邻接矩阵是对称矩阵？（ ）\nA．有向图 B．无向图 C．AOV网 D．AOE网\n8. 对具有n个节点、e条边且使用邻接表存储的有向图进行广度优先遍历，其算法时间复杂度（ ）。\nA.O(n) B.O(e) C.O(n+e) D.O(n*e)\n9.在所有的排序方法中，关键字比较的次数与记录的初始排列次序无关的是（ ）。\nA.希尔排序 B.冒泡排序 C.直接插入排序 D.直接选择排序\n10.对一组数据（2，12，16，88，5，10）进行排序，若前三趟排序结果如下,则采用的排序方法\n可能是（ ）。\n第一趟：2，12，16，5，10，88\n第二趟：2，12，5，10，16，88\n第三趟：2，5，10，12，16，88\nA. 希尔排序 B. 起泡排序 C.归并排序 D.基数排序\n三、简答题(共 7分)\n1. 写出下列二叉树的中序遍历序列，并将其改为中序线索二叉树。\n2. 请将这棵二叉树转换成对应的森林 。\nA\nA\nB C\nD E F\n＋------------------------------------------------------------------------------- ----------- 第2页 共4页 ＋", "char_count": 893}, {"page_number": 3, "text": "四、简答题(共 6 分)\n给定字母 a,b,c,d,e 的使用频率为 0.09, 0.17, 0.2, 0.23, 0.31。设计以该权值为基础的哈夫曼树，\n以及哈夫曼编码，并求其带权路径长度。\n-\n-\n:\n-\n-\n号 -\n- 五、简答题(共 3题，每题 5分，共 15 分)\n座 -\n-\n- 1. 输入关键字序列{12, 4, 1, 7, 8, 10}，给出构造一棵平衡二叉树的步骤。\n-\n- 2.序列的关键码为{46, 32, 55, 81, 65, 11, 25, 43}。 ，写出第一趟快速排序的结果。\n-\n-\n- 3.序列的关键码为{80,70,33,65,24,56,48}，请用筛选法建立最小堆。\n-\n-\n: -\n号 -\n- 六、简答题(共 6 分)\n室 -\n-\n教 - 设 散 列 表 的 长 度 为 13 ， 散 列 函 数 为 H(K)=K MOD 13 ， 给 定 的 关 键 字 序 列 为 ：\n场 -\n- 19，14，23，01，68，20，84。试画出用线性探测再散列解决冲突 时所构成的散列表，并求\n考 线\n- 等概率情况下查找成功时的平均查找长 度。\n-\n-\n-\n-\n- 七、简答题(共 12 分)\n-\n-\n- 对下列带权有向图，\n-\n-\n-\n1）写出其邻接表结构；\n-\n- 2）根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列；\n-\n： -\n师 - 3）利用Dijkstra算法，求图中顶点A到其它各顶点的最短路径，写出执行的过程。\n-\n教 -\n-\n课 -\n授 -\n- -\nA\n6\nB\n6\nC\n-\n-\n- C D G\n订\n- - 8 10 6 10 2\n-\n-\n-\n-\n-\n级 : - D E F\n年 - -\n- B\n2 5 G\n业 -\n专 -\n-\n- 八、简答题(共 10分)\n-\n-\n- 请写一算法，将带有头结点的非空单链表中数据域值最小的那个结点移到链表的最前面。\n-\n- 要求：不得额外申请新的链结点。\n-\n-\n-\n-\n-\n-\n九、简答题(共 4分)\n-\n: -\n名 -\n装\n姓 下面给出的算法功能是先序建立二叉树，请在下划线处填上正确的内容。\n-\n-\n- typedef struct node {\n-\n- char data；\n-\n-\n- struct node *leftchild, *rightchild；\n-\n- } BinTreeNode;\n-\n-\n-\n-\n-\n号 : - BinTreeNode *Crt_BinTree( )\n学 ＋------------------------------------------------------------------------------- ----------- 第3页 共4页 ＋", "char_count": 1154}, {"page_number": 4, "text": "{ char ch; BinTreeNode *t；\nprintf(\"please input data:\\n\")；\nscanf (\"%c\",&ch)；\nif (ch=='#') ___________ ; //输入#，表示空树\nelse {\nt=(BinTreeNode *) malloc(sizeof(BinTreeNode))；\nt->data = ch；\n______________________；\nt->rightchild=Crt_BinTree( )；\n}\nreturn (t)；\n}\n＋------------------------------------------------------------------------------- ----------- 第4页 共4页 ＋", "char_count": 355}], "tables": [{"page": 1, "table_index": 0, "data": [["", "-\n-\n-\n: -\n号 -\n-\n座 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n-\n室 -\n-\n教 -\n场 -\n-\n考 线\n-\n-\n-\n-\n-\n-\n-\n燕 -\n-\n海 -\n张 -\n-\n-\n-\n-\n： -\n师 -\n-\n教 -\n-\n课 -\n授 -\n-\n-\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n级 : -\n-\n年 -\n-\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", ""]]}, {"page": 1, "table_index": 1, "data": [["题号", "一", "二", "三", "四", "五", "六", "七", "八", "九", "总分"], ["得分", "", "", "", "", "", "", "", "", "", ""]]}, {"page": 3, "table_index": 0, "data": [["", "-\n-\n-\n: -\n号 -\n-\n座 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n-\n室 -\n-\n教 -\n场 -\n-\n考 线\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n： -\n师 -\n-\n教 -\n-\n课 -\n授 -\n-\n-\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n级 : -\n-\n年 -\n-\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", ""]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 4}}, "content_structure": {"filepath": "数据结构2016A.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2016A答案.pdf": {"extraction_info": {"filepath": "数据结构2016A答案.pdf", "method": "pdfplumber", "text": "2016 数据结构 A 卷答案\n一、填空题(共 10 题，每题 2 分，共 20 分)\n1) 14 2) 5 3) (a) 4) ABC*+DE/- 5)三元组顺序表\n6）先进先出 7）顺序 8）5 9）1423 10）归并\n二、选择题(共 10 题，每题 2 分，共 20 分)\nDCACD ABCDB\n三、简答题(共 7分)\n1、中序遍历序列DBAECF\n2、二叉树转换成对应的森林\nA C F\nA\nB\nE\nD\n四、简答题(共 6 分)\n100\n57 43\n26 31 20 23\n9 17\n<PERSON><PERSON><PERSON> 编码：0.09:000 0.17:001 0.2:10 0.23:11 0.31:01\n<PERSON><PERSON>man 编码带权路径长度 WPL=3*(0.09+0.17)+2*(0.2+0.23+0.31)=2.26\n五、简答题(共 3题，每题 5分，共 15 分)\n1、输入关键字序列{12, 4, 1, 7, 8, 10}，构造一棵平衡二叉树：\n4\n4\n12 A\nA\n1 8\n1 12\n4 B B\nB B\nB\n7 12\n7\n1 B\nB\nB\n10\n8\nB\n2、选择序列中46为轴值，第一趟快速排序的步骤：\n46, 32, 55, 81, 65, 11, 25, 43\n43, 32, 55, 81, 65, 11, 25, 46\n43, 32, 46, 81, 65, 11, 25, 55\n43, 32, 25, 81, 65, 11, 46, 55\n43, 32, 25, 46, 65, 11, 81, 55\n43, 32, 25, 11, 65, 46, 81, 55\n43, 32, 25, 11,46, 65, 81, 55\n3、关键码为{80,70,33,65,24,56,48}，用筛选法建立最小堆。\n80\n80\n24 33\n70 33\n65 70 56 48\n65 24 56 48\n24\n24\n65 33\n80 33\n80 70 56 48\n65 70 56 48\n六、简答题(共 6 分)\n将{19,14,23,01,68,20,84}依次存入散列表：\n19 mod 13=6 14 mod 13=1 23 mod 13=10 1 mod 13=1 68 mod 13=3\n20 mod 13=7 84 mod 13=6\n0 1 2 3 4 5 6 7 8 9 10 11 12\n14 1 68 19 20 84 23\n各数据的查找长度分别为19：1次，14：1次，23：1次，1：2次，68 :1次,\n20：1次，84：3次\n设查找等概率Pi=1/7, 因此平均查找长度为ASL=（1+1+1+2+1+1+3）/7=10/7\n七、简答题（12分）\n（1）该有向图的邻接表为:\n0 A 1 6 3 8\n∧\n1 B 2 6 4 6 5 10 ∧\n2 C 5 2 ∧\n3 D 1 10 4 2 ∧\n4 E 5 5 ∧\n5 F\n∧\n（2） 从顶点A出发深度优先遍历所得到的顶点序列为ABCFED\n（3） 从顶点A到其它各顶点的最短路径求解如下：\n步骤\ni=1 i=2 i=3 i=4 i=5\n节点\n{A,B}\nB\n6\n{A,B,C} {A,B,C} {A,B,C}\nC ∞\n12 12 12\n{A,D} {A,D}\nD\n8 8\n{A,B,E} {A,D,E}\nE ∞\n12 10\n∞ {A,B,F} {A,B,F} {A,D,E,F} {A,B,C,F}\nF\n16 16 15 14\n选取关键\nB D E C F\n路径点\nS (B) (B,D) (B,D,E) (B,D,E,C) (B,D,E,C,F)\n八、简答题(共 10分)\n函数原型如下：\nvoid delinsert(LinkList &L)\n{ p=L->next; //p 是链表的工作指针\npre=L; //pre 指向链表中数据域最小值结点的前驱\nq=p; //q 指向数据域最小值结点，初始假定是第一结点\nwhile(p->next!=NULL)\n{ if(p->next->data<q->data) //找到新的最小值结点\n{ pre=p; q=p->next; }\np=p->next; }\nif(q!=L->next) //若最小值是第一元素结点，则不需再操作\n{\npre->next=q->next; //将最小值结点从链表上摘下\nq->next=L->next; //将 q 结点插到链表最前面\nL->next=q;\n}\n}\n九、简答题(共 4分)\nt=NULL；\nt->rightchild=Crt_BinTree( )；", "pages": [{"page_number": 1, "text": "2016 数据结构 A 卷答案\n一、填空题(共 10 题，每题 2 分，共 20 分)\n1) 14 2) 5 3) (a) 4) ABC*+DE/- 5)三元组顺序表\n6）先进先出 7）顺序 8）5 9）1423 10）归并\n二、选择题(共 10 题，每题 2 分，共 20 分)\nDCACD ABCDB\n三、简答题(共 7分)\n1、中序遍历序列DBAECF\n2、二叉树转换成对应的森林\nA C F\nA\nB\nE\nD\n四、简答题(共 6 分)\n100\n57 43\n26 31 20 23\n9 17\nHuffman 编码：0.09:000 0.17:001 0.2:10 0.23:11 0.31:01\nHuffman 编码带权路径长度 WPL=3*(0.09+0.17)+2*(0.2+0.23+0.31)=2.26", "char_count": 357}, {"page_number": 2, "text": "五、简答题(共 3题，每题 5分，共 15 分)\n1、输入关键字序列{12, 4, 1, 7, 8, 10}，构造一棵平衡二叉树：\n4\n4\n12 A\nA\n1 8\n1 12\n4 B B\nB B\nB\n7 12\n7\n1 B\nB\nB\n10\n8\nB\n2、选择序列中46为轴值，第一趟快速排序的步骤：\n46, 32, 55, 81, 65, 11, 25, 43\n43, 32, 55, 81, 65, 11, 25, 46\n43, 32, 46, 81, 65, 11, 25, 55\n43, 32, 25, 81, 65, 11, 46, 55\n43, 32, 25, 46, 65, 11, 81, 55\n43, 32, 25, 11, 65, 46, 81, 55\n43, 32, 25, 11,46, 65, 81, 55\n3、关键码为{80,70,33,65,24,56,48}，用筛选法建立最小堆。\n80\n80\n24 33\n70 33\n65 70 56 48\n65 24 56 48", "char_count": 442}, {"page_number": 3, "text": "24\n24\n65 33\n80 33\n80 70 56 48\n65 70 56 48\n六、简答题(共 6 分)\n将{19,14,23,01,68,20,84}依次存入散列表：\n19 mod 13=6 14 mod 13=1 23 mod 13=10 1 mod 13=1 68 mod 13=3\n20 mod 13=7 84 mod 13=6\n0 1 2 3 4 5 6 7 8 9 10 11 12\n14 1 68 19 20 84 23\n各数据的查找长度分别为19：1次，14：1次，23：1次，1：2次，68 :1次,\n20：1次，84：3次\n设查找等概率Pi=1/7, 因此平均查找长度为ASL=（1+1+1+2+1+1+3）/7=10/7\n七、简答题（12分）\n（1）该有向图的邻接表为:\n0 A 1 6 3 8\n∧\n1 B 2 6 4 6 5 10 ∧\n2 C 5 2 ∧\n3 D 1 10 4 2 ∧\n4 E 5 5 ∧\n5 F\n∧", "char_count": 422}, {"page_number": 4, "text": "（2） 从顶点A出发深度优先遍历所得到的顶点序列为ABCFED\n（3） 从顶点A到其它各顶点的最短路径求解如下：\n步骤\ni=1 i=2 i=3 i=4 i=5\n节点\n{A,B}\nB\n6\n{A,B,C} {A,B,C} {A,B,C}\nC ∞\n12 12 12\n{A,D} {A,D}\nD\n8 8\n{A,B,E} {A,D,E}\nE ∞\n12 10\n∞ {A,B,F} {A,B,F} {A,D,E,F} {A,B,C,F}\nF\n16 16 15 14\n选取关键\nB D E C F\n路径点\nS (B) (B,D) (B,D,E) (B,D,E,C) (B,D,E,C,F)\n八、简答题(共 10分)\n函数原型如下：\nvoid delinsert(LinkList &L)\n{ p=L->next; //p 是链表的工作指针\npre=L; //pre 指向链表中数据域最小值结点的前驱\nq=p; //q 指向数据域最小值结点，初始假定是第一结点\nwhile(p->next!=NULL)\n{ if(p->next->data<q->data) //找到新的最小值结点\n{ pre=p; q=p->next; }\np=p->next; }\nif(q!=L->next) //若最小值是第一元素结点，则不需再操作\n{\npre->next=q->next; //将最小值结点从链表上摘下\nq->next=L->next; //将 q 结点插到链表最前面\nL->next=q;\n}\n}\n九、简答题(共 4分)\nt=NULL；\nt->rightchild=Crt_BinTree( )；", "char_count": 687}], "tables": [{"page": 1, "table_index": 0, "data": [["", "", ""]]}, {"page": 3, "table_index": 0, "data": [["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["", "14", "1", "68", "", "", "19", "20", "84", "", "23", "", ""]]}, {"page": 3, "table_index": 1, "data": [["A", ""], ["B", ""], ["C", ""], ["D", ""], ["E", ""], ["F", "∧"]]}, {"page": 3, "table_index": 2, "data": [["1", "6", ""]]}, {"page": 3, "table_index": 3, "data": [["3", "8", "∧"]]}, {"page": 3, "table_index": 4, "data": [["2", "6", ""]]}, {"page": 3, "table_index": 5, "data": [["4", "6", ""]]}, {"page": 3, "table_index": 6, "data": [["5", "10", "∧"]]}, {"page": 3, "table_index": 7, "data": [["5", "2", "∧"]]}, {"page": 3, "table_index": 8, "data": [["1", "10", ""]]}, {"page": 3, "table_index": 9, "data": [["4", "2", "∧"]]}, {"page": 3, "table_index": 10, "data": [["5", "5", "∧"]]}, {"page": 4, "table_index": 0, "data": [["步骤\n节点", "i=1", "i=2", "i=3", "i=4", "i=5"], ["B", "{<PERSON>,<PERSON>}\n6", "", "", "", ""], ["C", "∞", "{A,<PERSON>,<PERSON>}\n12", "{A,<PERSON>,<PERSON>}\n12", "{A,<PERSON>,<PERSON>}\n12", ""], ["D", "{A,D}\n8", "{A,D}\n8", "", "", ""], ["E", "∞", "{A,<PERSON>,<PERSON>}\n12", "{<PERSON>,<PERSON>,<PERSON>}\n10", "", ""], ["F", "∞", "{A,B,<PERSON>}\n16", "{A,B,<PERSON>}\n16", "{A,D,E,F}\n15", "{A,B,C,F}\n14"], ["选取关键\n路径点", "B", "D", "E", "C", "F"], ["S", "(B)", "(B,D)", "(B,D,E)", "(B,D,E,C)", "(B,D,E,C,F)"]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 4}}, "content_structure": {"filepath": "数据结构2016A答案.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2017A.pdf": {"extraction_info": {"filepath": "数据结构2017A.pdf", "method": "pdfplumber", "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n-\n- 2017年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n-\n-\n号 : - 试卷类型： A 卷 命题人: 审核人：________\n-\n座 -\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_4_页，除考场规定的必需用品外还可携带的文具有\n-\n- ______________。\n-\n: -\n号 - 题号 一 二 三 四 五 六 七 八 总分\n室 -\n-\n教 -\n场\n线 得分\n-\n考 -\n-\n-\n-\n- 一、填空题(共 10 题，每题 2 分，共 20 分)\n-\n-\n燕\n-\n-\n1. 循环单链表的最大优点是________________。\n- 2. 静态链表中指针表示的是________________。\n海 -\n-\n张 - 3. 递归过程或函数调用时，处理参数及返回地址，要用一种称为________的数据结构。\n-\n-\n- 4. 最大容量为n的循环队列，队尾指针是rear，队头是front，则队空的条件是________________。\n： -\n- 5. 数组的存储结构采用_______存储方式。\n师 -\n-\n教 - 6. 广义表L=（(x,y,z), a, (u,v,w)）的表尾是________________。\n课 -\n- 7. 在一棵三叉树中度为3的结点数为2个，度为2的结点数为1个，度为1的结点数为2个，\n授\n-\n-\n-\n订\n则度为0的结点数为_______个。\n8. 两个字符串相等的充分必要条件是________________。\n-\n-\n- 9. 引入二叉线索树的目的是________________。\n-\n- 10.利用树的孩子兄弟表示法存储，可以将一棵树转换为_________。\n-\n-\n-\n: -\n级 - 二、选择题(共 10 题，每题 2 分，共 20 分)\n-\n年 -\n业 - 1.以下数据结构中，( ) 是非线性数据结构。\n专 -\n- A. 树 B. 字符串 C. 队 D. 栈\n-\n-\n-\n-\n- 2.下列程序段的时间复杂度是 ( )。（请用大O表示法）\n-\n-\n- for (i=1; i<=n; i++)\n-\n-\n-\nfor (j=1; j<=i; j++)\n-\n: - for (k=1; k<=j; j++)\n名 -\n装\n姓 x+=c; (c为常数)\n-\n-\n-\nA. O(n) B. O(n2) C. O(n3) D. O(1)\n-\n-\n-\n-\n- 3.适用于折半查找的表的存储方式及元素排列要求为 ( ) 。\n-\n- A. 链式存储，元素无序 B. 链式存储，元素有序\n-\n-\n- C. 顺序存储，元素无序 D. 顺序存储，元素有序\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共4页 ＋\n4. 对序列{15，9，7，8，20，-1，4}，用希尔排序方法排序，经一趟排序后序列变为\n{15，-l，4，8，20，9，7}，则该趟排序采用的增量是 ( )。\nA. l B. 4 C. 3 D. 2\n5. 在下述结论中，正确的是 ( )。\n①只有一个结点的二叉树的度为0; ②二叉树的度为2；\n③二叉树的左右子树可以任意交换;\n④深度为K的完全二叉树的结点个数小于或等于深度相同的满二叉树。\nA.①②③ B.②③④ C.②④ D.①④\n6. 当采用分块查找时，数据的组织方式为 ( )。\nA.数据分成若干块，每块内数据有序\nB.数据分成若干块，每块内数据不必有序，但块间必须有序，每块内最大（或最小）的数据\n组成索引块\nC.数据分成若干块，每块内数据有序，每块内最大（或最小）的数据组成索引块\nD.数据分成若干块，每块（除最后一块外）中数据个数需相同\n7. 设无向图的顶点个数为n，则该图最多有 ( ) 条边。\nA.n-1 B.n(n-1)/2 C. n(n+1)/2 D.n2\n8. 下列关于AOE网的叙述中，不正确的是 ( )。\nA.关键活动不按期完成就会影响整个工程的完成时间\nB.任何一个关键活动提前完成，那么整个工程将会提前完成\nC.所有的关键活动提前完成，那么整个工程将会提前完成\nD.某些关键活动提前完成，那么整个工程将会提前完成\n9. 下面的排序算法中，稳定的算法是 ( ) 。\nA.起泡排序 B.快速排序 C.希尔排序 D.堆排序\n10. 求解最短路径的Floyd算法的时间复杂度为 ( )。\nA．O（n） B. O（n+c） C. O（n2） D. O（n3）\n三、简答题(共 7分)\n1. 一棵二叉树中序遍历序列和前序遍历序列分别是DCBAEFG和ABCDEFG，请画出该二叉树。\n2.用栈实现将中缀表达式8-(3+5)*(5-6/2)转换成后缀表达式。\n四、简答题(共 10 分)\n设用于通信的电文由8个字母组成, 字母在电文中出现的频次分别为: 7,19, 2, 6, 32, 3, 21,10。\n-\n- 试为这8个字母设计哈夫曼编码。使用0~7的二进制表示形式是另一种编码方案,试比较这两\n-\n-\n- 种方案的优缺点。\n-\n-\n-\n号 : -\n-\n座 -\n- ＋------------------------------------------------------------------------------- ----------- 第2页 共4页 ＋\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n室 -\n-\n教 -\n线\n场\n-\n考 -\n-\n-\n-\n-\n-\n-\n-\n燕 -\n-\n海 -\n-\n张 -\n-\n-\n-\n： -\n-\n师 -\n-\n教 -\n课 -\n-\n授\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n级 -\n-\n年 -\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学\n五、简答题(共 3题，每题 5分，共 15 分)\n1. 从空二叉树开始，根据字典顺序(注意：tea＜teach)，严格按照二叉排序树插入方法，依次\n插入head，he，tea，teach，twin，hot，toss。请画出插入所有结点后的二叉排序树。\n2.将关键码{195，14，527，68，121，46，57，575，60，89} ，按最低位优先法进行基数\n排序，进行一次分配和收集后得到的序列是多少？\n3.初始关键码序列为{5，4，24，11，8，12，13，3，16}，画出用筛选法建立的最大堆。\n六、简答题(共 6 分)\n设有一组记录的关键字为{27，19， 23， 68，20，84，55，11，10，79}，用链地址法构造\n哈希表，哈希函数为H（key）=key MOD 13, 并求等概率情况下查找成功时的平均查找长度。\n七、简答题(共 12 分)\n对下列带权图，若顶点在内存中以字母顺序存放，\n1）请写出其邻接表结构；\n2）根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列；\n3）请画出它的最小生成树，要有实现的步骤。（普里姆与克鲁斯卡尔算法任选之一 ）\nA\n5 5 B\nC\nG\nC\n6\n7\n1\nD 2\nD\n3\nF\nE\n4\nG\nB\n八、算法填空题(共 10分)\n设A和B是两个单链表(带头结点)，其中元素递增有序。下面给出的算法功能是，由A和B\n中公共相同的元素产生新的单链表C，要求不破坏A、B的结点。请在下划线处填上正确的内容。\ntypedef struct node { //链表结点\nint data; //结点数据域\nstruct node * next; //结点链域\n} Listnode;\n＋------------------------------------------------------------------------------- ----------- 第3页 共4页 ＋\ntypedef Listnode * Linklist;\nVoid common (Linklist A, Linklist B, Linklist &C)\n{ Listnode *p=A->next, *q=B->next, *r, *s;\nC= (Listnode *)malloc(sizeof(Listnode));\nr=C;\nwhile(p!=NULL &&q!=NULL)\n{ if (p->data < q->data)\np=p->next;\nelse if ((p->data > q->data)\nq=q->next;\nelse\n{s= (Listnode *)malloc(sizeof(Listnode));\ns->data = p->data;\n_________;\n_________;\n_________;\n_________;\n}\n}\nr->next=NULL;\n}\n＋------------------------------------------------------------------------------- ----------- 第4页 共4页 ＋", "pages": [{"page_number": 1, "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n-\n- 2017年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n-\n-\n号 : - 试卷类型： A 卷 命题人: 审核人：________\n-\n座 -\n-\n-\n-\n-\n-\n考试说明：本课程为闭卷考试，共_4_页，除考场规定的必需用品外还可携带的文具有\n-\n- ______________。\n-\n: -\n号 - 题号 一 二 三 四 五 六 七 八 总分\n室 -\n-\n教 -\n场\n线 得分\n-\n考 -\n-\n-\n-\n- 一、填空题(共 10 题，每题 2 分，共 20 分)\n-\n-\n燕\n-\n-\n1. 循环单链表的最大优点是________________。\n- 2. 静态链表中指针表示的是________________。\n海 -\n-\n张 - 3. 递归过程或函数调用时，处理参数及返回地址，要用一种称为________的数据结构。\n-\n-\n- 4. 最大容量为n的循环队列，队尾指针是rear，队头是front，则队空的条件是________________。\n： -\n- 5. 数组的存储结构采用_______存储方式。\n师 -\n-\n教 - 6. 广义表L=（(x,y,z), a, (u,v,w)）的表尾是________________。\n课 -\n- 7. 在一棵三叉树中度为3的结点数为2个，度为2的结点数为1个，度为1的结点数为2个，\n授\n-\n-\n-\n订\n则度为0的结点数为_______个。\n8. 两个字符串相等的充分必要条件是________________。\n-\n-\n- 9. 引入二叉线索树的目的是________________。\n-\n- 10.利用树的孩子兄弟表示法存储，可以将一棵树转换为_________。\n-\n-\n-\n: -\n级 - 二、选择题(共 10 题，每题 2 分，共 20 分)\n-\n年 -\n业 - 1.以下数据结构中，( ) 是非线性数据结构。\n专 -\n- A. 树 B. 字符串 C. 队 D. 栈\n-\n-\n-\n-\n- 2.下列程序段的时间复杂度是 ( )。（请用大O表示法）\n-\n-\n- for (i=1; i<=n; i++)\n-\n-\n-\nfor (j=1; j<=i; j++)\n-\n: - for (k=1; k<=j; j++)\n名 -\n装\n姓 x+=c; (c为常数)\n-\n-\n-\nA. O(n) B. O(n2) C. O(n3) D. O(1)\n-\n-\n-\n-\n- 3.适用于折半查找的表的存储方式及元素排列要求为 ( ) 。\n-\n- A. 链式存储，元素无序 B. 链式存储，元素有序\n-\n-\n- C. 顺序存储，元素无序 D. 顺序存储，元素有序\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共4页 ＋", "char_count": 1258}, {"page_number": 2, "text": "4. 对序列{15，9，7，8，20，-1，4}，用希尔排序方法排序，经一趟排序后序列变为\n{15，-l，4，8，20，9，7}，则该趟排序采用的增量是 ( )。\nA. l B. 4 C. 3 D. 2\n5. 在下述结论中，正确的是 ( )。\n①只有一个结点的二叉树的度为0; ②二叉树的度为2；\n③二叉树的左右子树可以任意交换;\n④深度为K的完全二叉树的结点个数小于或等于深度相同的满二叉树。\nA.①②③ B.②③④ C.②④ D.①④\n6. 当采用分块查找时，数据的组织方式为 ( )。\nA.数据分成若干块，每块内数据有序\nB.数据分成若干块，每块内数据不必有序，但块间必须有序，每块内最大（或最小）的数据\n组成索引块\nC.数据分成若干块，每块内数据有序，每块内最大（或最小）的数据组成索引块\nD.数据分成若干块，每块（除最后一块外）中数据个数需相同\n7. 设无向图的顶点个数为n，则该图最多有 ( ) 条边。\nA.n-1 B.n(n-1)/2 C. n(n+1)/2 D.n2\n8. 下列关于AOE网的叙述中，不正确的是 ( )。\nA.关键活动不按期完成就会影响整个工程的完成时间\nB.任何一个关键活动提前完成，那么整个工程将会提前完成\nC.所有的关键活动提前完成，那么整个工程将会提前完成\nD.某些关键活动提前完成，那么整个工程将会提前完成\n9. 下面的排序算法中，稳定的算法是 ( ) 。\nA.起泡排序 B.快速排序 C.希尔排序 D.堆排序\n10. 求解最短路径的Floyd算法的时间复杂度为 ( )。\nA．O（n） B. O（n+c） C. O（n2） D. O（n3）\n三、简答题(共 7分)\n1. 一棵二叉树中序遍历序列和前序遍历序列分别是DCBAEFG和ABCDEFG，请画出该二叉树。\n2.用栈实现将中缀表达式8-(3+5)*(5-6/2)转换成后缀表达式。\n四、简答题(共 10 分)\n设用于通信的电文由8个字母组成, 字母在电文中出现的频次分别为: 7,19, 2, 6, 32, 3, 21,10。\n-\n- 试为这8个字母设计哈夫曼编码。使用0~7的二进制表示形式是另一种编码方案,试比较这两\n-\n-\n- 种方案的优缺点。\n-\n-\n-\n号 : -\n-\n座 -\n- ＋------------------------------------------------------------------------------- ----------- 第2页 共4页 ＋\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n室 -\n-\n教 -\n线\n场\n-\n考 -\n-\n-\n-\n-\n-\n-\n-\n燕 -\n-\n海 -\n-\n张 -\n-\n-\n-\n： -\n-\n师 -\n-\n教 -\n课 -\n-\n授\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n级 -\n-\n年 -\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", "char_count": 1272}, {"page_number": 3, "text": "五、简答题(共 3题，每题 5分，共 15 分)\n1. 从空二叉树开始，根据字典顺序(注意：tea＜teach)，严格按照二叉排序树插入方法，依次\n插入head，he，tea，teach，twin，hot，toss。请画出插入所有结点后的二叉排序树。\n2.将关键码{195，14，527，68，121，46，57，575，60，89} ，按最低位优先法进行基数\n排序，进行一次分配和收集后得到的序列是多少？\n3.初始关键码序列为{5，4，24，11，8，12，13，3，16}，画出用筛选法建立的最大堆。\n六、简答题(共 6 分)\n设有一组记录的关键字为{27，19， 23， 68，20，84，55，11，10，79}，用链地址法构造\n哈希表，哈希函数为H（key）=key MOD 13, 并求等概率情况下查找成功时的平均查找长度。\n七、简答题(共 12 分)\n对下列带权图，若顶点在内存中以字母顺序存放，\n1）请写出其邻接表结构；\n2）根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列；\n3）请画出它的最小生成树，要有实现的步骤。（普里姆与克鲁斯卡尔算法任选之一 ）\nA\n5 5 B\nC\nG\nC\n6\n7\n1\nD 2\nD\n3\nF\nE\n4\nG\nB\n八、算法填空题(共 10分)\n设A和B是两个单链表(带头结点)，其中元素递增有序。下面给出的算法功能是，由A和B\n中公共相同的元素产生新的单链表C，要求不破坏A、B的结点。请在下划线处填上正确的内容。\ntypedef struct node { //链表结点\nint data; //结点数据域\nstruct node * next; //结点链域\n} Listnode;\n＋------------------------------------------------------------------------------- ----------- 第3页 共4页 ＋", "char_count": 821}, {"page_number": 4, "text": "typedef Listnode * Linklist;\nVoid common (Linklist A, Linklist B, Linklist &C)\n{ Listnode *p=A->next, *q=B->next, *r, *s;\nC= (Listnode *)malloc(sizeof(Listnode));\nr=C;\nwhile(p!=NULL &&q!=NULL)\n{ if (p->data < q->data)\np=p->next;\nelse if ((p->data > q->data)\nq=q->next;\nelse\n{s= (Listnode *)malloc(sizeof(Listnode));\ns->data = p->data;\n_________;\n_________;\n_________;\n_________;\n}\n}\nr->next=NULL;\n}\n＋------------------------------------------------------------------------------- ----------- 第4页 共4页 ＋", "char_count": 501}], "tables": [{"page": 1, "table_index": 0, "data": [["", "-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n-\n座 -\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n室 -\n-\n教 -\n线\n场\n-\n考 -\n-\n-\n-\n-\n-\n-\n-\n燕 -\n-\n海 -\n-\n张 -\n-\n-\n-\n： -\n-\n师 -\n-\n教 -\n课 -\n-\n授\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n级 -\n-\n年 -\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", ""]]}, {"page": 1, "table_index": 1, "data": [["题号", "一", "二", "三", "四", "五", "六", "七", "八", "总分"], ["得分", "", "", "", "", "", "", "", "", ""]]}, {"page": 2, "table_index": 0, "data": [["", "-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n-\n座 -\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n室 -\n-\n教 -\n线\n场\n-\n考 -\n-\n-\n-\n-\n-\n-\n-\n燕 -\n-\n海 -\n-\n张 -\n-\n-\n-\n： -\n-\n师 -\n-\n教 -\n课 -\n-\n授\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n级 -\n-\n年 -\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", ""]]}, {"page": 3, "table_index": 0, "data": [["7", ""]]}, {"page": 3, "table_index": 1, "data": [[null, "2"], ["3", null]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 4}}, "content_structure": {"filepath": "数据结构2017A.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}, "数据结构2018A.pdf": {"extraction_info": {"filepath": "数据结构2018A.pdf", "method": "pdfplumber", "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n-\n-\n-\n2018年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n-\n- 试卷类型： A 卷 命题人: 审核人：________\n-\n-\n-\n: -\n号 -\n座 - 考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n- ______________。\n-\n-\n- 题号 一 二 三 四 五 六 七 八 总分\n-\n-\n: -\n号 线 得分\n室 -\n-\n教 -\n场 -\n-\n考 - 一、填空题(共 20 分，每题 2 分)\n-\n-\n- 1.从逻辑上可以把数据结构分为_________和_________两大类。\n-\n- 2.在高级语言实现的程序中，调用函数和被调用函数之间参数的传递需要一种称为________的\n-\n-\n- 数据结构来进行。\n-\n-\n- 3.在长度为n的顺序表中插入一个元素的时间复杂度是_________。\n： -\n- 4.串的模式匹配是指________________。\n师 -\n-\n教 - 5.广义表L=（a，（b，c）），进行Head（Tail（L））操作后的结果为_________。\n课 -\n- 6.一棵度为3的树T中，若有10个度为3的结点，1个度为2的结点，10个度为1的结点，\n授\n-\n-\n-\n订\n则树T的叶子结点个数是_________。\n7.一棵高度为4的完全二叉树至少有_________个结点。\n-\n-\n- 8.折半查找法要求查找表必须是_________存储的有序表。\n-\n- 9.在一棵m阶的B-树中，每个结点最多有_________棵子树。\n-\n-\n- 10.对n个关键字进行堆排序，最坏情况下其时间复杂度也为_________。\n: -\n级 -\n-\n年 -\n业 - 二、选择题(共 20 分，每题 2 分)\n专 -\n- 1.程序段for（i=n-1;i>=1,--i)\n-\n-\n- for (j=1;j<=i;++j)\n-\n- If (A[j]>A[j+1]) A[j]与A[j+1] 对换；\n-\n-\n- 其中n为正整数，则最后一行的语句频度在最坏情况下是( )。\n-\n-\n-\nA. O(n) B. O(nlog\n2\nn) C. O(n3) D. O(n2)\n-\n: -\n名 -\n装\n姓 2.如果为线性表分配较大的连续空间，插入和删除操作不需要移动元素，存储结构采用( )。\n-\n-\n-\nA. 单链表 B. 静态链表 C. 顺序表 D. 双链表\n-\n-\n-\n-\n- 3.若元素a,b,c,d,e,f依次进栈，允许进栈和出栈操作交替进行，但不允许连续3次进行\n-\n- 出栈操作，则不可能得到的出栈序列是( )。\n-\n-\n- A. d,c,e,b,f,a B. c,b,d,a,e,f C. b,c,a,e,f,d D. a,f,e,d,c,b\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共3页 ＋\n4.栈和队列都是( )。\nA.顺序存储的线性结构 B.链式存储的非线性结构\nC.限制存取位置的线性结构 D.限制存取位置的非线性结构\n5.稀疏矩阵一般的压缩存储方法有( )两种。\nA.二维数组和十字链表 B.三元组和散列表\nC.三元组和十字链表 D. 散列表和十字链表\n6. 与单链表相比，双链表的优点之一是( )。\nA.插入和删除操作更简单 B.可以进行随机访问\nC.可以省略表头指针或表尾指针 D.访问前后相邻结点更加灵活\n7.下列关于图的叙述中，正确的是( )。\n①回路是简单路径\n②存储稀疏图，用邻接矩阵比邻接表更节省空间\n③若有向图中存在拓扑排序，则该图不存在回路\nA.仅① B.仅 ②③ C.仅③ D.仅① ③\n8.对于下列关键字序列，不可能构成某棵二叉排序树中一条查找路径的序列是( )。\nA.95,22,91,24,94,71 B. 92,20,91,34,88,35\nC. 21,89,77,29,36,38 D.12,25,71,68,33,34\n9.对于含有n个顶点的带权连通图，它的最小生成树是指图中任意一个( )。\nA. 由n-1条权值最小的边构成的子图\nB. 由n-1条权值之和最小的边构成的子图\nC. 由n-1条权值之和最小的边构成的连通子图\nD. 由n个顶点构成的边的权值之和最小的连通子图\n10.排序趟数与序列的初始状态有关的排序算法是( )。\nA.直接插入排序 B.起泡排序 C.归并排序 D.简单选择排序\n三、简答题(共 10分，每题 5分)\n1、一棵二叉树前序遍历序列是E,F,H,I,G,J,K,中序遍历序列是H,F,I,E,J,K,G，请画出该二叉树。\n2、若将关键字{16, 3, 7, 11, 9, 26}依次插入到初始为空的平衡二叉树中，请画出插入所有关\n键字的平衡二叉树的实现过程。\n四、简答题(10 分)\n若对这样一串字符“AAABBEEACCCDEEA”进行压缩存储，请设计赫夫曼（Huffman）编码，\n并与三位等长的二进制编码方案做比较。\n＋------------------------------------------------------------------------------- ----------- 第2页 共3页 ＋\n五、简答题(共 10分，每题 5分)\n1.有一组关键字{46, 79, 56, 38, 40, 84}，利用快速排序，写出以第一个关键字为基准得到\n的第一趟排序结果。（要有实现的步骤）\n2.对给定的关键字序列{110, 319, 007, 931, 264, 120, 122}进行基数排序，写出第2趟分配收\n集后的关键字序列。\n六、简答题(8 分)\n将关键字序列为{17, 19, 61, 98, 75, 63,46}存储到哈希表中，哈希表的存储空间是下标从0开\n始的长度为10的一维数组，哈希函数为H(key)=key MOD 7, 处理冲突采用线性探测再散列\n法。请画出所构造的哈希表，并求等概率情况下查找成功时的平均查找长度。\n七、简答题(共 12 分)\n对下列带权有向图，\n1）请写出其邻接表存储结构；\n2）根据邻接表结构，写出从顶点A出发深度优先遍历所得到的顶点序列；\n3）利用迪杰斯特拉 Dijkstra 算法，求图中顶点 A 到其它各顶点的最短路径，写出求解的\n过程。\n2\nA E\nC 6 3 D\n9 B 7\nG\n1 2\nC D\n2\nB G\n八、算法题(10分)\n在带头结点head 的非空单链表中，设计一个尽可能高效的算法，删除指针 p 所指向的结点，\n并说明算法的时间复杂度。\n提示：删除结点常用的方法是让前驱结点的指针指向该结点的后继，另外也可以把该结点后\n继的数据复制到该结点，然后删除此后继结点（但如果是最后一个结点，只能用常见的方法）\ntypedef struct node { //链表结点\nint data; //结点数据域\nstruct node * next; //结点指针域\n} Listnode;\nTypedef Listnode * Linklist;\n＋------------------------------------------------------------------------------- ----------- 第3页 共3页 ＋", "pages": [{"page_number": 1, "text": "中国海洋大学全日制本科课程期末考试试卷\n-\n-\n-\n-\n-\n-\n2018年 春 季学期 考试科目： 数据结构 学院： 信息科学与工程学院\n-\n-\n- 试卷类型： A 卷 命题人: 审核人：________\n-\n-\n-\n: -\n号 -\n座 - 考试说明：本课程为闭卷考试，共_3_页，除考场规定的必需用品外还可携带的文具有\n-\n-\n- ______________。\n-\n-\n- 题号 一 二 三 四 五 六 七 八 总分\n-\n-\n: -\n号 线 得分\n室 -\n-\n教 -\n场 -\n-\n考 - 一、填空题(共 20 分，每题 2 分)\n-\n-\n- 1.从逻辑上可以把数据结构分为_________和_________两大类。\n-\n- 2.在高级语言实现的程序中，调用函数和被调用函数之间参数的传递需要一种称为________的\n-\n-\n- 数据结构来进行。\n-\n-\n- 3.在长度为n的顺序表中插入一个元素的时间复杂度是_________。\n： -\n- 4.串的模式匹配是指________________。\n师 -\n-\n教 - 5.广义表L=（a，（b，c）），进行Head（Tail（L））操作后的结果为_________。\n课 -\n- 6.一棵度为3的树T中，若有10个度为3的结点，1个度为2的结点，10个度为1的结点，\n授\n-\n-\n-\n订\n则树T的叶子结点个数是_________。\n7.一棵高度为4的完全二叉树至少有_________个结点。\n-\n-\n- 8.折半查找法要求查找表必须是_________存储的有序表。\n-\n- 9.在一棵m阶的B-树中，每个结点最多有_________棵子树。\n-\n-\n- 10.对n个关键字进行堆排序，最坏情况下其时间复杂度也为_________。\n: -\n级 -\n-\n年 -\n业 - 二、选择题(共 20 分，每题 2 分)\n专 -\n- 1.程序段for（i=n-1;i>=1,--i)\n-\n-\n- for (j=1;j<=i;++j)\n-\n- If (A[j]>A[j+1]) A[j]与A[j+1] 对换；\n-\n-\n- 其中n为正整数，则最后一行的语句频度在最坏情况下是( )。\n-\n-\n-\nA. O(n) B. O(nlog\n2\nn) C. O(n3) D. O(n2)\n-\n: -\n名 -\n装\n姓 2.如果为线性表分配较大的连续空间，插入和删除操作不需要移动元素，存储结构采用( )。\n-\n-\n-\nA. 单链表 B. 静态链表 C. 顺序表 D. 双链表\n-\n-\n-\n-\n- 3.若元素a,b,c,d,e,f依次进栈，允许进栈和出栈操作交替进行，但不允许连续3次进行\n-\n- 出栈操作，则不可能得到的出栈序列是( )。\n-\n-\n- A. d,c,e,b,f,a B. c,b,d,a,e,f C. b,c,a,e,f,d D. a,f,e,d,c,b\n-\n-\n号 : -\n学\n＋------------------------------------------------------------------------------- ----------- 第1页 共3页 ＋", "char_count": 1318}, {"page_number": 2, "text": "4.栈和队列都是( )。\nA.顺序存储的线性结构 B.链式存储的非线性结构\nC.限制存取位置的线性结构 D.限制存取位置的非线性结构\n5.稀疏矩阵一般的压缩存储方法有( )两种。\nA.二维数组和十字链表 B.三元组和散列表\nC.三元组和十字链表 D. 散列表和十字链表\n6. 与单链表相比，双链表的优点之一是( )。\nA.插入和删除操作更简单 B.可以进行随机访问\nC.可以省略表头指针或表尾指针 D.访问前后相邻结点更加灵活\n7.下列关于图的叙述中，正确的是( )。\n①回路是简单路径\n②存储稀疏图，用邻接矩阵比邻接表更节省空间\n③若有向图中存在拓扑排序，则该图不存在回路\nA.仅① B.仅 ②③ C.仅③ D.仅① ③\n8.对于下列关键字序列，不可能构成某棵二叉排序树中一条查找路径的序列是( )。\nA.95,22,91,24,94,71 B. 92,20,91,34,88,35\nC. 21,89,77,29,36,38 D.12,25,71,68,33,34\n9.对于含有n个顶点的带权连通图，它的最小生成树是指图中任意一个( )。\nA. 由n-1条权值最小的边构成的子图\nB. 由n-1条权值之和最小的边构成的子图\nC. 由n-1条权值之和最小的边构成的连通子图\nD. 由n个顶点构成的边的权值之和最小的连通子图\n10.排序趟数与序列的初始状态有关的排序算法是( )。\nA.直接插入排序 B.起泡排序 C.归并排序 D.简单选择排序\n三、简答题(共 10分，每题 5分)\n1、一棵二叉树前序遍历序列是E,F,H,I,G,J,K,中序遍历序列是H,F,I,E,J,K,G，请画出该二叉树。\n2、若将关键字{16, 3, 7, 11, 9, 26}依次插入到初始为空的平衡二叉树中，请画出插入所有关\n键字的平衡二叉树的实现过程。\n四、简答题(10 分)\n若对这样一串字符“AAABBEEACCCDEEA”进行压缩存储，请设计赫夫曼（Huffman）编码，\n并与三位等长的二进制编码方案做比较。\n＋------------------------------------------------------------------------------- ----------- 第2页 共3页 ＋", "char_count": 952}, {"page_number": 3, "text": "五、简答题(共 10分，每题 5分)\n1.有一组关键字{46, 79, 56, 38, 40, 84}，利用快速排序，写出以第一个关键字为基准得到\n的第一趟排序结果。（要有实现的步骤）\n2.对给定的关键字序列{110, 319, 007, 931, 264, 120, 122}进行基数排序，写出第2趟分配收\n集后的关键字序列。\n六、简答题(8 分)\n将关键字序列为{17, 19, 61, 98, 75, 63,46}存储到哈希表中，哈希表的存储空间是下标从0开\n始的长度为10的一维数组，哈希函数为H(key)=key MOD 7, 处理冲突采用线性探测再散列\n法。请画出所构造的哈希表，并求等概率情况下查找成功时的平均查找长度。\n七、简答题(共 12 分)\n对下列带权有向图，\n1）请写出其邻接表存储结构；\n2）根据邻接表结构，写出从顶点A出发深度优先遍历所得到的顶点序列；\n3）利用迪杰斯特拉 Dijkstra 算法，求图中顶点 A 到其它各顶点的最短路径，写出求解的\n过程。\n2\nA E\nC 6 3 D\n9 B 7\nG\n1 2\nC D\n2\nB G\n八、算法题(10分)\n在带头结点head 的非空单链表中，设计一个尽可能高效的算法，删除指针 p 所指向的结点，\n并说明算法的时间复杂度。\n提示：删除结点常用的方法是让前驱结点的指针指向该结点的后继，另外也可以把该结点后\n继的数据复制到该结点，然后删除此后继结点（但如果是最后一个结点，只能用常见的方法）\ntypedef struct node { //链表结点\nint data; //结点数据域\nstruct node * next; //结点指针域\n} Listnode;\nTypedef Listnode * Linklist;\n＋------------------------------------------------------------------------------- ----------- 第3页 共3页 ＋", "char_count": 850}], "tables": [{"page": 1, "table_index": 0, "data": [["", "-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 -\n座 -\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n号 线\n室 -\n-\n教 -\n场 -\n-\n考 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n： -\n-\n师 -\n-\n教 -\n课 -\n-\n授\n-\n-\n-\n订\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n级 -\n-\n年 -\n业 -\n专 -\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n: -\n名 -\n装\n姓\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n-\n号 : -\n学", ""]]}, {"page": 1, "table_index": 1, "data": [["题号", "一", "二", "三", "四", "五", "六", "七", "八", "总分"], ["得分", "", "", "", "", "", "", "", "", ""]]}, {"page": 3, "table_index": 0, "data": [["9", ""]]}], "metadata": {"title": "", "author": "", "subject": "", "creator": "Microsoft® Word 2021", "pages_count": 3}}, "content_structure": {"filepath": "数据结构2018A.pdf", "questions": [], "sections": [], "total_questions": 0, "question_types": {}}, "status": "success"}}}