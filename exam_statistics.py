#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考点统计分析模块
计算知识点频率、重要度排序、年度趋势分析
"""

import json
import csv
import logging
from typing import Dict, List, Tuple, Any
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
import math

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class KnowledgePointStats:
    """知识点统计数据"""
    category: str
    subcategory: str
    frequency: int  # 出现频率
    total_weight: float  # 总权重
    avg_weight: float  # 平均权重
    years_appeared: List[int]  # 出现年份
    question_types: Dict[str, int]  # 题型分布
    difficulty_distribution: Dict[str, int]  # 难度分布
    importance_score: float  # 重要度得分
    rank: int  # 排名

@dataclass
class StatisticsReport:
    """统计报告"""
    total_questions: int
    total_knowledge_points: int
    years_analyzed: List[int]
    top_knowledge_points: List[KnowledgePointStats]
    yearly_trends: Dict[int, Dict[str, Any]]
    difficulty_analysis: Dict[str, Any]
    type_analysis: Dict[str, Any]
    recommendations: List[str]

class ExamStatistics:
    """考点统计分析器"""
    
    def __init__(self):
        self.questions_data = None
        self.knowledge_stats = {}
        
    def load_questions_data(self) -> bool:
        """加载题目分析数据"""
        try:
            with open('questions_analysis.json', 'r', encoding='utf-8') as f:
                self.questions_data = json.load(f)
            logger.info(f"成功加载 {self.questions_data['total_questions']} 道题目数据")
            return True
        except FileNotFoundError:
            logger.error("未找到 questions_analysis.json 文件")
            return False
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return False
    
    def calculate_frequency(self) -> Dict[str, KnowledgePointStats]:
        """计算知识点频率统计"""
        logger.info("开始计算知识点频率")
        
        knowledge_stats = defaultdict(lambda: {
            'frequency': 0,
            'total_weight': 0.0,
            'years_appeared': set(),
            'question_types': defaultdict(int),
            'difficulty_distribution': defaultdict(int),
            'weights': []
        })
        
        # 遍历所有题目
        for question in self.questions_data.get('questions_data', []):
            year = question.get('year', 2024)
            qtype = question.get('type', '未知')
            difficulty = question.get('difficulty', '未知')
            knowledge_points = question.get('knowledge_points', [])
            
            for kp in knowledge_points:
                if len(kp) >= 3:  # [主分类, 子分类, 权重]
                    main_cat, sub_cat, weight = kp[0], kp[1], kp[2]
                    key = f"{main_cat}-{sub_cat}"
                    
                    stats = knowledge_stats[key]
                    stats['frequency'] += 1
                    stats['total_weight'] += weight
                    stats['years_appeared'].add(year)
                    stats['question_types'][qtype] += 1
                    stats['difficulty_distribution'][difficulty] += 1
                    stats['weights'].append(weight)
        
        # 转换为KnowledgePointStats对象
        result = {}
        for key, stats in knowledge_stats.items():
            if '-' in key and stats['frequency'] > 0:
                main_cat, sub_cat = key.split('-', 1)
                
                avg_weight = stats['total_weight'] / stats['frequency']
                
                kp_stats = KnowledgePointStats(
                    category=main_cat,
                    subcategory=sub_cat,
                    frequency=stats['frequency'],
                    total_weight=stats['total_weight'],
                    avg_weight=avg_weight,
                    years_appeared=sorted(list(stats['years_appeared'])),
                    question_types=dict(stats['question_types']),
                    difficulty_distribution=dict(stats['difficulty_distribution']),
                    importance_score=0.0,  # 稍后计算
                    rank=0  # 稍后计算
                )
                
                result[key] = kp_stats
        
        logger.info(f"计算完成，共 {len(result)} 个知识点")
        return result
    
    def calculate_importance_score(self, kp_stats: KnowledgePointStats, 
                                 total_questions: int) -> float:
        """计算知识点重要度得分"""
        # 频率得分 (0-40分)
        frequency_score = min(40, (kp_stats.frequency / total_questions) * 200)
        
        # 权重得分 (0-30分)
        weight_score = min(30, kp_stats.avg_weight * 50)
        
        # 年份覆盖得分 (0-20分)
        year_coverage = len(kp_stats.years_appeared) / 5  # 总共5年
        coverage_score = year_coverage * 20
        
        # 难度分布得分 (0-10分)
        difficulty_score = 0
        if '困难' in kp_stats.difficulty_distribution:
            difficulty_score += 5
        if '中等' in kp_stats.difficulty_distribution:
            difficulty_score += 3
        if '简单' in kp_stats.difficulty_distribution:
            difficulty_score += 2
        
        total_score = frequency_score + weight_score + coverage_score + difficulty_score
        return min(100, total_score)
    
    def rank_knowledge_points(self, knowledge_stats: Dict[str, KnowledgePointStats]) -> List[KnowledgePointStats]:
        """按重要度排序知识点"""
        logger.info("开始排序知识点")
        
        total_questions = self.questions_data.get('total_questions', 1)
        
        # 计算重要度得分
        for stats in knowledge_stats.values():
            stats.importance_score = self.calculate_importance_score(stats, total_questions)
        
        # 按重要度排序
        sorted_stats = sorted(knowledge_stats.values(), 
                            key=lambda x: x.importance_score, reverse=True)
        
        # 分配排名
        for i, stats in enumerate(sorted_stats, 1):
            stats.rank = i
        
        logger.info(f"排序完成，共 {len(sorted_stats)} 个知识点")
        return sorted_stats
    
    def analyze_yearly_trends(self) -> Dict[int, Dict[str, Any]]:
        """分析年度趋势"""
        logger.info("开始分析年度趋势")
        
        yearly_trends = {}
        
        # 按年份分组
        questions_by_year = defaultdict(list)
        for question in self.questions_data.get('questions_data', []):
            year = question.get('year', 2024)
            questions_by_year[year].append(question)
        
        for year, questions in questions_by_year.items():
            year_stats = {
                'total_questions': len(questions),
                'type_distribution': defaultdict(int),
                'difficulty_distribution': defaultdict(int),
                'knowledge_categories': defaultdict(int),
                'avg_knowledge_points': 0
            }
            
            total_kp_count = 0
            for question in questions:
                # 题型分布
                qtype = question.get('type', '未知')
                year_stats['type_distribution'][qtype] += 1
                
                # 难度分布
                difficulty = question.get('difficulty', '未知')
                year_stats['difficulty_distribution'][difficulty] += 1
                
                # 知识点分布
                knowledge_points = question.get('knowledge_points', [])
                total_kp_count += len(knowledge_points)
                
                for kp in knowledge_points:
                    if len(kp) >= 1:
                        main_cat = kp[0]
                        year_stats['knowledge_categories'][main_cat] += 1
            
            # 计算平均知识点数
            year_stats['avg_knowledge_points'] = total_kp_count / len(questions) if questions else 0
            
            # 转换为普通字典
            year_stats['type_distribution'] = dict(year_stats['type_distribution'])
            year_stats['difficulty_distribution'] = dict(year_stats['difficulty_distribution'])
            year_stats['knowledge_categories'] = dict(year_stats['knowledge_categories'])
            
            yearly_trends[year] = year_stats
        
        logger.info(f"年度趋势分析完成，覆盖 {len(yearly_trends)} 年")
        return yearly_trends
    
    def generate_difficulty_analysis(self) -> Dict[str, Any]:
        """生成难度分析"""
        difficulty_stats = defaultdict(int)
        difficulty_by_type = defaultdict(lambda: defaultdict(int))
        difficulty_by_year = defaultdict(lambda: defaultdict(int))
        
        for question in self.questions_data.get('questions_data', []):
            difficulty = question.get('difficulty', '未知')
            qtype = question.get('type', '未知')
            year = question.get('year', 2024)
            
            difficulty_stats[difficulty] += 1
            difficulty_by_type[qtype][difficulty] += 1
            difficulty_by_year[year][difficulty] += 1
        
        return {
            'overall_distribution': dict(difficulty_stats),
            'by_question_type': {qtype: dict(dist) for qtype, dist in difficulty_by_type.items()},
            'by_year': {year: dict(dist) for year, dist in difficulty_by_year.items()}
        }
    
    def generate_type_analysis(self) -> Dict[str, Any]:
        """生成题型分析"""
        type_stats = defaultdict(int)
        type_by_year = defaultdict(lambda: defaultdict(int))
        type_by_difficulty = defaultdict(lambda: defaultdict(int))
        
        for question in self.questions_data.get('questions_data', []):
            qtype = question.get('type', '未知')
            year = question.get('year', 2024)
            difficulty = question.get('difficulty', '未知')
            
            type_stats[qtype] += 1
            type_by_year[year][qtype] += 1
            type_by_difficulty[difficulty][qtype] += 1
        
        return {
            'overall_distribution': dict(type_stats),
            'by_year': {year: dict(dist) for year, dist in type_by_year.items()},
            'by_difficulty': {diff: dict(dist) for diff, dist in type_by_difficulty.items()}
        }
    
    def generate_recommendations(self, top_knowledge_points: List[KnowledgePointStats]) -> List[str]:
        """生成学习建议"""
        recommendations = []
        
        if not top_knowledge_points:
            return ["暂无足够数据生成建议"]
        
        # 基于高频知识点的建议
        top_5 = top_knowledge_points[:5]
        high_freq_points = [f"{kp.category}-{kp.subcategory}" for kp in top_5]
        recommendations.append(f"🎯 重点掌握前5个高频考点：{', '.join(high_freq_points)}")
        
        # 基于年份覆盖的建议
        consistent_points = [kp for kp in top_knowledge_points if len(kp.years_appeared) >= 4]
        if consistent_points:
            consistent_names = [f"{kp.category}-{kp.subcategory}" for kp in consistent_points[:3]]
            recommendations.append(f"📈 重点关注连续多年出现的知识点：{', '.join(consistent_names)}")
        
        # 基于难度分布的建议
        difficult_points = [kp for kp in top_knowledge_points 
                          if '困难' in kp.difficulty_distribution and kp.difficulty_distribution['困难'] > 0]
        if difficult_points:
            difficult_names = [f"{kp.category}-{kp.subcategory}" for kp in difficult_points[:3]]
            recommendations.append(f"💪 加强练习困难题型涉及的知识点：{', '.join(difficult_names)}")
        
        # 基于题型分布的建议
        total_questions = self.questions_data.get('total_questions', 0)
        type_dist = self.questions_data.get('by_type', {})
        if type_dist:
            most_common_type = max(type_dist.items(), key=lambda x: x[1])
            recommendations.append(f"📝 {most_common_type[0]}是最常见题型({most_common_type[1]}道)，需重点练习")
        
        # 时间分配建议
        recommendations.append("⏰ 建议复习时间分配：高频考点60%，中频考点30%，低频考点10%")
        
        # 复习策略建议
        recommendations.append("📚 复习策略：先理解概念，再练习题目，最后总结规律")
        
        return recommendations
    
    def create_statistics_report(self) -> StatisticsReport:
        """创建完整统计报告"""
        logger.info("开始创建统计报告")
        
        # 计算知识点统计
        knowledge_stats = self.calculate_frequency()
        
        # 排序知识点
        ranked_knowledge_points = self.rank_knowledge_points(knowledge_stats)
        
        # 分析年度趋势
        yearly_trends = self.analyze_yearly_trends()
        
        # 生成难度和题型分析
        difficulty_analysis = self.generate_difficulty_analysis()
        type_analysis = self.generate_type_analysis()
        
        # 生成建议
        recommendations = self.generate_recommendations(ranked_knowledge_points)
        
        # 创建报告
        report = StatisticsReport(
            total_questions=self.questions_data.get('total_questions', 0),
            total_knowledge_points=len(knowledge_stats),
            years_analyzed=sorted(self.questions_data.get('by_year', {}).keys()),
            top_knowledge_points=ranked_knowledge_points[:20],  # 前20个
            yearly_trends=yearly_trends,
            difficulty_analysis=difficulty_analysis,
            type_analysis=type_analysis,
            recommendations=recommendations
        )
        
        logger.info("统计报告创建完成")
        return report
    
    def export_statistics(self, report: StatisticsReport):
        """导出统计数据"""
        logger.info("开始导出统计数据")
        
        # 导出JSON格式报告
        with open('statistics_report.json', 'w', encoding='utf-8') as f:
            json.dump(asdict(report), f, ensure_ascii=False, indent=2, default=str)
        logger.info("JSON报告已保存到 statistics_report.json")
        
        # 导出CSV格式的知识点排序
        with open('knowledge_ranking.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '排名', '主分类', '子分类', '频率', '总权重', '平均权重',
                '出现年份', '重要度得分', '题型分布', '难度分布'
            ])
            
            # 写入数据
            for kp in report.top_knowledge_points:
                writer.writerow([
                    kp.rank,
                    kp.category,
                    kp.subcategory,
                    kp.frequency,
                    f"{kp.total_weight:.2f}",
                    f"{kp.avg_weight:.2f}",
                    ','.join(map(str, kp.years_appeared)),
                    f"{kp.importance_score:.2f}",
                    str(kp.question_types),
                    str(kp.difficulty_distribution)
                ])
        
        logger.info("CSV排序表已保存到 knowledge_ranking.csv")
    
    def run_complete_analysis(self) -> StatisticsReport:
        """运行完整统计分析"""
        logger.info("=== 开始完整统计分析 ===")
        
        # 加载数据
        if not self.load_questions_data():
            logger.error("数据加载失败")
            return None
        
        # 创建统计报告
        report = self.create_statistics_report()
        
        # 导出结果
        self.export_statistics(report)
        
        logger.info("=== 统计分析完成 ===")
        return report


def test_exam_statistics():
    """测试统计分析器"""
    print("=== 考点统计分析器测试 ===")
    
    analyzer = ExamStatistics()
    
    # 运行完整分析
    report = analyzer.run_complete_analysis()
    
    if report:
        print(f"✅ 分析完成!")
        print(f"📊 总题目数: {report.total_questions}")
        print(f"🔍 总知识点数: {report.total_knowledge_points}")
        print(f"📅 分析年份: {report.years_analyzed}")
        
        print(f"\n🏆 前5个重要知识点:")
        for i, kp in enumerate(report.top_knowledge_points[:5], 1):
            print(f"  {i}. {kp.category}-{kp.subcategory} (重要度: {kp.importance_score:.1f}, 频率: {kp.frequency})")
        
        print(f"\n💡 学习建议:")
        for i, rec in enumerate(report.recommendations[:3], 1):
            print(f"  {i}. {rec}")
        
        return True
    else:
        print("❌ 分析失败!")
        return False


if __name__ == "__main__":
    success = test_exam_statistics()
    print(f"\n考点统计分析器{'测试成功' if success else '测试失败'}!")
