排名,主分类,子分类,频率,总权重,平均权重,出现年份,重要度得分,题型分布,难度分布
1,线性表,链表,6,3.25,0.54,"2014,2016,2017,2018",71.83,"{'选择题': 1, '填空题': 4, '算法题': 1}","{'简单': 4, '中等': 1, '困难': 1}"
2,树结构,哈夫曼树,7,3.25,0.46,"2014,2015,2016,2017,2018",70.09,"{'计算题': 3, '简答题': 4}","{'简单': 5, '中等': 2}"
3,树结构,堆,7,3.50,0.50,"2014,2016,2017,2018",69.88,"{'填空题': 3, '计算题': 3, '选择题': 1}","{'简单': 6, '困难': 1}"
4,图结构,图表示,10,3.20,0.32,"2014,2015,2016,2018",68.25,"{'计算题': 3, '填空题': 1, '简答题': 6}","{'简单': 9, '中等': 1}"
5,查找算法,哈希表,6,2.73,0.46,"2014,2015,2016,2017,2018",66.50,"{'简答题': 4, '计算题': 2}","{'中等': 3, '简单': 3}"
6,排序算法,高级排序,9,1.82,0.20,"2014,2015,2016,2017,2018",65.24,"{'填空题': 3, '选择题': 4, '计算题': 2}","{'简单': 8, '困难': 1}"
7,树结构,遍历,5,1.62,0.33,"2014,2015,2016,2018",49.88,"{'计算题': 2, '简答题': 3}",{'简单': 5}
8,排序算法,简单排序,2,0.70,0.35,"2015,2018",33.75,{'选择题': 2},{'简单': 2}
9,排序算法,线性排序,3,0.30,0.10,"2014,2016,2018",28.38,"{'填空题': 1, '选择题': 1, '计算题': 1}",{'简单': 3}
10,树结构,二叉树,1,0.35,0.35,2014,26.62,{'填空题': 1},{'简单': 1}
11,线性表,数组,1,0.30,0.30,2018,24.12,{'计算题': 1},{'简单': 1}
12,算法分析,复杂度,1,0.12,0.12,2018,15.12,{'填空题': 1},{'简单': 1}
13,排序算法,排序分析,1,0.10,0.10,2018,14.12,{'填空题': 1},{'简单': 1}
