#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构真题内容分析引擎
智能解析考试内容，识别题型，提取知识点，分析难度
"""

import re
import os
import json
import logging
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from knowledge_system import knowledge_system

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ExamQuestion:
    """考试题目数据类"""
    id: str
    type: str  # 题目类型：选择题、填空题、简答题、算法题
    content: str  # 题目内容
    options: List[str]  # 选择题选项
    answer: str  # 答案
    knowledge_points: List[Tuple[str, str, float]]  # (分类, 子分类, 权重)
    difficulty: str  # 难度等级：简单、中等、困难
    score: int  # 分值
    year: int  # 年份
    section: str  # 题目所属部分
    raw_text: str  # 原始文本

class ContentAnalyzer:
    """内容分析引擎类"""

    def __init__(self):
        self.knowledge_system = knowledge_system
        self.question_patterns = self._build_question_patterns()
        self.difficulty_indicators = self._build_difficulty_indicators()
        self.score_patterns = self._build_score_patterns()

    def _build_question_patterns(self) -> Dict[str, List[re.Pattern]]:
        """构建题目类型识别模式"""
        return {
            "选择题": [
                re.compile(r'[ABCD][\.\)]\s*[^\n]+', re.MULTILINE),
                re.compile(r'选择题|单选题|多选题', re.IGNORECASE),
                re.compile(r'\d+[\.\)]\s*[^ABCD]*[ABCD][\.\)]\s*[^\n]+', re.MULTILINE),
                re.compile(r'下列.*正确的是|以下.*错误的是|关于.*说法正确的是', re.IGNORECASE)
            ],
            "填空题": [
                re.compile(r'_{3,}|_+\s*_+', re.MULTILINE),
                re.compile(r'填空题|空白处', re.IGNORECASE),
                re.compile(r'在.*填入|请填写|补充完整', re.IGNORECASE),
                re.compile(r'\(\s*\)|（\s*）', re.MULTILINE)
            ],
            "简答题": [
                re.compile(r'简答题|问答题|解答题', re.IGNORECASE),
                re.compile(r'请.*说明|试.*分析|简述|阐述', re.IGNORECASE),
                re.compile(r'什么是|如何.*|为什么', re.IGNORECASE),
                re.compile(r'比较.*异同|分析.*优缺点', re.IGNORECASE)
            ],
            "算法题": [
                re.compile(r'算法题|编程题|设计题', re.IGNORECASE),
                re.compile(r'写出.*算法|设计.*算法|实现.*算法', re.IGNORECASE),
                re.compile(r'伪代码|pseudocode|代码实现', re.IGNORECASE),
                re.compile(r'时间复杂度.*空间复杂度|复杂度分析', re.IGNORECASE)
            ],
            "计算题": [
                re.compile(r'计算题|求解|计算.*结果', re.IGNORECASE),
                re.compile(r'画出.*图|绘制.*树|构造.*表', re.IGNORECASE),
                re.compile(r'排序过程|查找过程|遍历过程', re.IGNORECASE),
                re.compile(r'给定.*求|已知.*计算', re.IGNORECASE)
            ]
        }

    def _build_difficulty_indicators(self) -> Dict[str, List[str]]:
        """构建难度指示词"""
        return {
            "简单": [
                "基本概念", "定义", "简单", "直接", "基础",
                "什么是", "列举", "说出", "写出",
                "顺序查找", "冒泡排序", "数组访问"
            ],
            "中等": [
                "分析", "比较", "设计", "实现", "应用",
                "二分查找", "快速排序", "二叉树遍历",
                "哈希表", "图的遍历", "动态规划"
            ],
            "困难": [
                "优化", "证明", "复杂", "综合", "创新",
                "平衡树", "最短路径", "最小生成树",
                "复杂度分析", "算法改进", "系统设计"
            ]
        }

    def _build_score_patterns(self) -> List[re.Pattern]:
        """构建分值识别模式"""
        return [
            re.compile(r'(\d+)\s*分', re.IGNORECASE),
            re.compile(r'\(\s*(\d+)\s*分\s*\)', re.IGNORECASE),
            re.compile(r'(\d+)\s*points?', re.IGNORECASE),
            re.compile(r'分值\s*[:：]\s*(\d+)', re.IGNORECASE)
        ]

    def parse_exam_content(self, text: str, year: int) -> List[ExamQuestion]:
        """
        解析考试内容结构

        Args:
            text: 考试文本内容
            year: 年份

        Returns:
            解析出的题目列表
        """
        logger.info(f"开始解析{year}年考试内容")

        # 预处理文本
        cleaned_text = self._preprocess_text(text)

        # 分割题目
        questions_raw = self._split_questions(cleaned_text)

        # 解析每道题目
        questions = []
        for i, question_text in enumerate(questions_raw):
            if len(question_text.strip()) < 20:  # 过滤过短的文本
                continue

            question = self._parse_single_question(
                question_text,
                f"{year}_{i+1}",
                year
            )
            if question:
                questions.append(question)

        logger.info(f"解析完成，共识别{len(questions)}道题目")
        return questions

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 移除过多的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符和格式标记
        text = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u00a0-\u00ff\n\r\t\(\)\[\]（）【】]', '', text)

        # 标准化标点符号
        text = text.replace('（', '(').replace('）', ')')
        text = text.replace('【', '[').replace('】', ']')

        return text.strip()

    def _split_questions(self, text: str) -> List[str]:
        """分割题目"""
        # 尝试多种分割方式
        questions = []

        # 方式1：按题号分割
        pattern1 = re.compile(r'(\d+[\.\)]\s*[^0-9])', re.MULTILINE)
        parts = pattern1.split(text)

        if len(parts) > 3:  # 如果成功分割
            current_question = ""
            for i, part in enumerate(parts):
                if pattern1.match(part):
                    if current_question:
                        questions.append(current_question)
                    current_question = part
                else:
                    current_question += part
            if current_question:
                questions.append(current_question)
        else:
            # 方式2：按段落分割
            paragraphs = text.split('\n')
            current_question = ""

            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                if not paragraph:
                    continue

                # 检查是否是新题目的开始
                if (re.match(r'\d+[\.\)]', paragraph) or
                    any(keyword in paragraph for keyword in ['选择题', '填空题', '简答题', '算法题'])):
                    if current_question and len(current_question) > 50:
                        questions.append(current_question)
                    current_question = paragraph
                else:
                    current_question += " " + paragraph

            if current_question and len(current_question) > 50:
                questions.append(current_question)

        return questions

    def _parse_single_question(self, question_text: str, question_id: str, year: int) -> Optional[ExamQuestion]:
        """解析单道题目"""
        try:
            # 识别题目类型
            question_type = self.identify_question_type(question_text)

            # 提取选择题选项
            options = self._extract_options(question_text) if question_type == "选择题" else []

            # 提取答案（如果有）
            answer = self._extract_answer(question_text)

            # 提取知识点
            knowledge_points = self.extract_knowledge_points(question_text)

            # 分析难度
            difficulty = self.analyze_difficulty(question_text, knowledge_points)

            # 提取分值
            score = self._extract_score(question_text)

            # 确定题目所属部分
            section = self._determine_section(question_text, knowledge_points)

            return ExamQuestion(
                id=question_id,
                type=question_type,
                content=self._clean_question_content(question_text),
                options=options,
                answer=answer,
                knowledge_points=knowledge_points,
                difficulty=difficulty,
                score=score,
                year=year,
                section=section,
                raw_text=question_text
            )

        except Exception as e:
            logger.warning(f"解析题目失败: {e}")
            return None

    def identify_question_type(self, question: str) -> str:
        """识别题目类型"""
        type_scores = defaultdict(int)

        for question_type, patterns in self.question_patterns.items():
            for pattern in patterns:
                matches = pattern.findall(question)
                type_scores[question_type] += len(matches)

        # 特殊规则
        if re.search(r'[ABCD][\.\)]\s*[^\n]+.*[ABCD][\.\)]\s*[^\n]+', question, re.DOTALL):
            type_scores["选择题"] += 5

        if re.search(r'_{3,}', question):
            type_scores["填空题"] += 3

        if re.search(r'算法|伪代码|时间复杂度', question, re.IGNORECASE):
            type_scores["算法题"] += 3

        # 返回得分最高的类型
        if type_scores:
            return max(type_scores.items(), key=lambda x: x[1])[0]
        else:
            return "简答题"  # 默认类型

    def extract_knowledge_points(self, question: str) -> List[Tuple[str, str, float]]:
        """提取知识点"""
        analysis = self.knowledge_system.analyze_text_knowledge_points(question)

        knowledge_points = []
        for category, subcategory, keyword, count in analysis["knowledge_points"]:
            # 计算权重
            base_weight = self.knowledge_system.importance_weights.get(category, 0.1)
            sub_weight = self.knowledge_system.importance_weights.get(subcategory, 1.0)
            final_weight = base_weight * sub_weight * count

            knowledge_points.append((category, subcategory, final_weight))

        # 去重并合并相同的知识点
        merged_points = defaultdict(float)
        for category, subcategory, weight in knowledge_points:
            key = f"{category}-{subcategory}"
            merged_points[key] += weight

        # 转换回列表格式并排序
        result = []
        for key, weight in merged_points.items():
            category, subcategory = key.split('-', 1)
            result.append((category, subcategory, weight))

        # 按权重排序
        result.sort(key=lambda x: x[2], reverse=True)

        return result[:5]  # 返回前5个最重要的知识点

    def analyze_difficulty(self, question: str, knowledge_points: List[Tuple[str, str, float]]) -> str:
        """分析题目难度"""
        difficulty_scores = {"简单": 0, "中等": 0, "困难": 0}

        # 基于关键词分析
        for difficulty, indicators in self.difficulty_indicators.items():
            for indicator in indicators:
                if indicator in question:
                    difficulty_scores[difficulty] += 1

        # 基于知识点复杂度
        if knowledge_points:
            avg_weight = sum(weight for _, _, weight in knowledge_points) / len(knowledge_points)
            if avg_weight > 2.0:
                difficulty_scores["困难"] += 2
            elif avg_weight > 1.0:
                difficulty_scores["中等"] += 2
            else:
                difficulty_scores["简单"] += 1

        # 基于题目长度
        if len(question) > 500:
            difficulty_scores["困难"] += 1
        elif len(question) > 200:
            difficulty_scores["中等"] += 1
        else:
            difficulty_scores["简单"] += 1

        # 返回得分最高的难度
        return max(difficulty_scores.items(), key=lambda x: x[1])[0]

    def _extract_options(self, question: str) -> List[str]:
        """提取选择题选项"""
        options = []

        # 匹配 A. B. C. D. 格式
        pattern = re.compile(r'([ABCD])[\.\)]\s*([^\n\r]+)', re.MULTILINE)
        matches = pattern.findall(question)

        for option_letter, option_text in matches:
            options.append(f"{option_letter}. {option_text.strip()}")

        return options

    def _extract_answer(self, question: str) -> str:
        """提取答案"""
        # 查找答案模式
        answer_patterns = [
            re.compile(r'答案\s*[:：]\s*([ABCD])', re.IGNORECASE),
            re.compile(r'正确答案\s*[:：]\s*([ABCD])', re.IGNORECASE),
            re.compile(r'\(([ABCD])\)', re.IGNORECASE),
            re.compile(r'选择\s*([ABCD])', re.IGNORECASE)
        ]

        for pattern in answer_patterns:
            match = pattern.search(question)
            if match:
                return match.group(1)

        return ""

    def _extract_score(self, question: str) -> int:
        """提取分值"""
        for pattern in self.score_patterns:
            match = pattern.search(question)
            if match:
                try:
                    return int(match.group(1))
                except ValueError:
                    continue

        # 默认分值（基于题目类型估算）
        if "选择题" in question:
            return 2
        elif "填空题" in question:
            return 3
        elif "算法题" in question or "设计" in question:
            return 10
        else:
            return 5

    def _determine_section(self, question: str, knowledge_points: List[Tuple[str, str, float]]) -> str:
        """确定题目所属部分"""
        if knowledge_points:
            # 基于主要知识点确定部分
            main_category = knowledge_points[0][0]
            return main_category

        # 基于关键词确定
        if any(keyword in question for keyword in ["选择", "单选", "多选"]):
            return "选择题部分"
        elif any(keyword in question for keyword in ["填空", "空白"]):
            return "填空题部分"
        elif any(keyword in question for keyword in ["算法", "设计", "编程"]):
            return "算法题部分"
        else:
            return "综合题部分"

    def _clean_question_content(self, question: str) -> str:
        """清理题目内容"""
        # 移除多余的空白字符
        content = re.sub(r'\s+', ' ', question)

        # 移除题号
        content = re.sub(r'^\d+[\.\)]\s*', '', content)

        # 移除答案部分
        content = re.sub(r'答案\s*[:：].*$', '', content, flags=re.IGNORECASE)

        return content.strip()

    def batch_analyze_exams(self) -> Dict[int, List[ExamQuestion]]:
        """批量分析所有年份真题"""
        logger.info("开始批量分析所有年份真题")

        results = {}
        extracted_texts_dir = "extracted_texts"

        if not os.path.exists(extracted_texts_dir):
            logger.error(f"提取文本目录不存在: {extracted_texts_dir}")
            return results

        # 处理每个年份的文件
        year_files = {
            2014: ["数据结构2014A.txt", "数据结构2014A答案.txt"],
            2015: ["数据结构2015A.txt"],
            2016: ["数据结构2016A.txt", "数据结构2016A答案.txt"],
            2017: ["数据结构2017A.txt"],
            2018: ["数据结构2018A.txt"]
        }

        for year, files in year_files.items():
            year_questions = []

            for filename in files:
                filepath = os.path.join(extracted_texts_dir, filename)
                if os.path.exists(filepath):
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            content = f.read()

                        questions = self.parse_exam_content(content, year)
                        year_questions.extend(questions)

                        logger.info(f"处理完成: {filename}, 识别题目: {len(questions)}道")

                    except Exception as e:
                        logger.error(f"处理文件失败 {filepath}: {e}")
                else:
                    logger.warning(f"文件不存在: {filepath}")

            results[year] = year_questions
            logger.info(f"{year}年共识别题目: {len(year_questions)}道")

        # 保存分析结果
        self._save_analysis_results(results)

        return results

    def _save_analysis_results(self, results: Dict[int, List[ExamQuestion]]):
        """保存分析结果"""
        output_dir = "analysis_results"
        os.makedirs(output_dir, exist_ok=True)

        # 保存详细结果
        for year, questions in results.items():
            output_file = os.path.join(output_dir, f"questions_{year}.json")
            questions_data = [asdict(q) for q in questions]

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(questions_data, f, ensure_ascii=False, indent=2)

            logger.info(f"保存{year}年分析结果到: {output_file}")

        # 保存汇总统计
        summary = self._generate_summary_statistics(results)
        summary_file = os.path.join(output_dir, "analysis_summary.json")

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        logger.info(f"保存汇总统计到: {summary_file}")

    def _generate_summary_statistics(self, results: Dict[int, List[ExamQuestion]]) -> Dict:
        """生成汇总统计"""
        total_questions = sum(len(questions) for questions in results.values())

        # 统计题型分布
        type_stats = defaultdict(int)
        difficulty_stats = defaultdict(int)
        knowledge_stats = defaultdict(int)

        for questions in results.values():
            for question in questions:
                type_stats[question.type] += 1
                difficulty_stats[question.difficulty] += 1

                for category, subcategory, weight in question.knowledge_points:
                    knowledge_stats[category] += 1

        return {
            "total_questions": total_questions,
            "years_analyzed": list(results.keys()),
            "type_distribution": dict(type_stats),
            "difficulty_distribution": dict(difficulty_stats),
            "knowledge_distribution": dict(knowledge_stats),
            "questions_per_year": {year: len(questions) for year, questions in results.items()}
        }


def test_content_analyzer():
    """测试内容分析引擎"""
    print("=== 内容分析引擎测试 ===")

    analyzer = ContentAnalyzer()

    # 测试文本
    test_text = """
    1. 下列关于栈的说法正确的是（）
    A. 栈是先进先出的数据结构
    B. 栈是后进先出的数据结构
    C. 栈可以随机访问元素
    D. 栈的插入和删除操作都在栈底进行

    2. 二叉树的中序遍历序列是_______，前序遍历序列是_______。

    3. 请设计一个算法，实现二分查找，并分析其时间复杂度。
    """

    # 解析测试
    questions = analyzer.parse_exam_content(test_text, 2024)

    print(f"识别题目数量: {len(questions)}")

    for i, question in enumerate(questions, 1):
        print(f"\n题目 {i}:")
        print(f"  类型: {question.type}")
        print(f"  难度: {question.difficulty}")
        print(f"  分值: {question.score}")
        print(f"  知识点: {[f'{cat}-{sub}' for cat, sub, _ in question.knowledge_points[:3]]}")
        print(f"  内容: {question.content[:100]}...")

    return len(questions) > 0


if __name__ == "__main__":
    # 运行测试
    success = test_content_analyzer()
    print(f"\n内容分析引擎{'测试成功' if success else '测试失败'}!")

    if success:
        # 运行批量分析
        print("\n=== 开始批量分析所有真题 ===")
        analyzer = ContentAnalyzer()
        results = analyzer.batch_analyze_exams()

        print(f"\n批量分析完成!")
        for year, questions in results.items():
            print(f"{year}年: {len(questions)}道题目")

        total = sum(len(questions) for questions in results.values())
        print(f"总计: {total}道题目")
