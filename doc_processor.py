#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构真题文档处理器
用于读取Word文档(.doc格式)并提取纯文本内容
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from typing import List, Dict, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('doc_processor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DocProcessor:
    """Word文档处理器类"""

    def __init__(self):
        self.supported_extensions = ['.doc', '.docx']
        self.output_dir = 'extracted_texts'
        self.ensure_output_dir()

    def ensure_output_dir(self):
        """确保输出目录存在"""
        Path(self.output_dir).mkdir(exist_ok=True)
        logger.info(f"输出目录已准备: {self.output_dir}")

    def read_doc_file(self, filepath: str) -> Optional[str]:
        """
        读取Word文档文件并返回纯文本内容

        Args:
            filepath: 文档文件路径

        Returns:
            提取的文本内容，失败时返回None
        """
        if not os.path.exists(filepath):
            logger.error(f"文件不存在: {filepath}")
            return None

        logger.info(f"开始处理文档: {filepath}")

        # 方法1: 尝试使用textract
        text = self._try_textract(filepath)
        if text:
            logger.info(f"textract成功提取文本: {len(text)}字符")
            return text

        # 方法2: 尝试使用docx2txt (适用于.docx)
        if filepath.lower().endswith('.docx'):
            text = self._try_docx2txt(filepath)
            if text:
                logger.info(f"docx2txt成功提取文本: {len(text)}字符")
                return text

        # 方法3: 尝试使用python-docx (仅适用于.docx)
        if filepath.lower().endswith('.docx'):
            text = self._try_python_docx(filepath)
            if text:
                logger.info(f"python-docx成功提取文本: {len(text)}字符")
                return text

        # 方法4: 尝试简单的二进制读取和文本提取 (适用于.doc)
        if filepath.lower().endswith('.doc'):
            text = self._try_binary_extraction(filepath)
            if text:
                logger.info(f"二进制提取成功提取文本: {len(text)}字符")
                return text

        # 方法5: 尝试使用antiword (适用于.doc)
        if filepath.lower().endswith('.doc'):
            text = self._try_antiword(filepath)
            if text:
                logger.info(f"antiword成功提取文本: {len(text)}字符")
                return text

        # 方法6: 尝试使用LibreOffice命令行转换
        text = self._try_libreoffice(filepath)
        if text:
            logger.info(f"LibreOffice成功提取文本: {len(text)}字符")
            return text

        logger.error(f"所有方法都无法提取文档内容: {filepath}")
        return None

    def _try_textract(self, filepath: str) -> Optional[str]:
        """尝试使用textract库提取文本"""
        try:
            import textract
            text = textract.process(filepath).decode('utf-8')
            return text.strip()
        except ImportError:
            logger.warning("textract库未安装，跳过...")
            return None
        except Exception as e:
            logger.warning(f"textract提取失败: {e}")
            return None

    def _try_docx2txt(self, filepath: str) -> Optional[str]:
        """尝试使用docx2txt库提取文本"""
        try:
            import docx2txt
            text = docx2txt.process(filepath)
            return text.strip() if text else None
        except ImportError:
            logger.warning("docx2txt库未安装，跳过...")
            return None
        except Exception as e:
            logger.warning(f"docx2txt提取失败: {e}")
            return None

    def _try_binary_extraction(self, filepath: str) -> Optional[str]:
        """尝试从.doc文件中进行简单的二进制文本提取"""
        try:
            import re
            with open(filepath, 'rb') as f:
                content = f.read()

            # 尝试解码为UTF-8，忽略错误
            try:
                text = content.decode('utf-8', errors='ignore')
            except:
                # 如果UTF-8失败，尝试GBK
                try:
                    text = content.decode('gbk', errors='ignore')
                except:
                    # 最后尝试latin-1
                    text = content.decode('latin-1', errors='ignore')

            # 清理文本：移除控制字符，保留中文、英文、数字和常见标点
            cleaned_text = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u00a0-\u00ff\n\r\t]', '', text)

            # 移除过多的空白字符
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

            # 提取看起来像是有意义的文本段落
            lines = cleaned_text.split('\n')
            meaningful_lines = []

            for line in lines:
                line = line.strip()
                # 保留包含中文或长度超过10个字符的行
                if len(line) > 10 or re.search(r'[\u4e00-\u9fff]', line):
                    meaningful_lines.append(line)

            result = '\n'.join(meaningful_lines).strip()

            # 如果提取的文本太少，可能提取失败
            if len(result) < 50:
                logger.warning(f"二进制提取的文本内容太少: {len(result)}字符")
                return None

            return result

        except Exception as e:
            logger.warning(f"二进制提取失败: {e}")
            return None

    def _try_python_docx(self, filepath: str) -> Optional[str]:
        """尝试使用python-docx库提取文本"""
        try:
            from docx import Document
            doc = Document(filepath)
            text = []
            for paragraph in doc.paragraphs:
                text.append(paragraph.text)
            return '\n'.join(text).strip()
        except ImportError:
            logger.warning("python-docx库未安装，尝试安装...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'python-docx'])
                from docx import Document
                doc = Document(filepath)
                text = []
                for paragraph in doc.paragraphs:
                    text.append(paragraph.text)
                return '\n'.join(text).strip()
            except Exception as e:
                logger.warning(f"python-docx安装或使用失败: {e}")
                return None
        except Exception as e:
            logger.warning(f"python-docx提取失败: {e}")
            return None

    def _try_antiword(self, filepath: str) -> Optional[str]:
        """尝试使用antiword命令行工具提取文本"""
        try:
            result = subprocess.run(
                ['antiword', filepath],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                logger.warning(f"antiword执行失败: {result.stderr}")
                return None
        except FileNotFoundError:
            logger.warning("antiword命令未找到")
            return None
        except Exception as e:
            logger.warning(f"antiword执行异常: {e}")
            return None

    def _try_libreoffice(self, filepath: str) -> Optional[str]:
        """尝试使用LibreOffice命令行转换为文本"""
        try:
            # 创建临时目录
            temp_dir = Path('temp_conversion')
            temp_dir.mkdir(exist_ok=True)

            # 使用LibreOffice转换为文本格式
            result = subprocess.run([
                'soffice', '--headless', '--convert-to', 'txt',
                '--outdir', str(temp_dir), filepath
            ], capture_output=True, text=True)

            if result.returncode == 0:
                # 查找生成的txt文件
                txt_file = temp_dir / (Path(filepath).stem + '.txt')
                if txt_file.exists():
                    with open(txt_file, 'r', encoding='utf-8') as f:
                        text = f.read().strip()
                    # 清理临时文件
                    txt_file.unlink()
                    return text

            logger.warning(f"LibreOffice转换失败: {result.stderr}")
            return None
        except FileNotFoundError:
            logger.warning("LibreOffice命令未找到")
            return None
        except Exception as e:
            logger.warning(f"LibreOffice转换异常: {e}")
            return None

    def save_text_to_file(self, text: str, original_filepath: str) -> str:
        """
        将提取的文本保存到文件

        Args:
            text: 提取的文本内容
            original_filepath: 原始文档路径

        Returns:
            保存的文本文件路径
        """
        filename = Path(original_filepath).stem + '.txt'
        output_path = Path(self.output_dir) / filename

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)

        logger.info(f"文本已保存到: {output_path}")
        return str(output_path)

    def batch_process_docs(self) -> Dict[str, str]:
        """
        批量处理所有Word文档

        Returns:
            处理结果字典，键为原文件路径，值为提取的文本文件路径
        """
        # 定义要处理的文档列表
        doc_files = [
            '数据结构2014A.doc',
            '数据结构2014A答案.doc',
            '数据结构2015A.doc',
            '数据结构2016A.doc',
            '数据结构2016A答案.doc',
            '数据结构2017A.doc',
            '数据结构2018A.doc'
        ]

        results = {}
        success_count = 0

        logger.info(f"开始批量处理 {len(doc_files)} 个文档")

        for doc_file in doc_files:
            if not os.path.exists(doc_file):
                logger.error(f"文档文件不存在: {doc_file}")
                continue

            # 提取文本
            text = self.read_doc_file(doc_file)
            if text:
                # 保存文本文件
                output_path = self.save_text_to_file(text, doc_file)
                results[doc_file] = output_path
                success_count += 1
                logger.info(f"✓ 成功处理: {doc_file}")
            else:
                logger.error(f"✗ 处理失败: {doc_file}")
                results[doc_file] = None

        logger.info(f"批量处理完成: {success_count}/{len(doc_files)} 个文档成功")
        return results

    def validate_extraction(self, results: Dict[str, str]) -> bool:
        """
        验证提取结果的完整性

        Args:
            results: 处理结果字典

        Returns:
            验证是否通过
        """
        logger.info("开始验证提取结果...")

        success_files = [k for k, v in results.items() if v is not None]
        failed_files = [k for k, v in results.items() if v is None]

        if failed_files:
            logger.error(f"以下文件提取失败: {failed_files}")
            return False

        # 检查文本文件是否存在且有内容
        for original_file, text_file in results.items():
            if text_file and os.path.exists(text_file):
                with open(text_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if len(content) < 100:  # 内容太少可能有问题
                        logger.warning(f"文件 {original_file} 提取的内容可能不完整 (仅{len(content)}字符)")
                    else:
                        logger.info(f"✓ {original_file} 提取内容验证通过 ({len(content)}字符)")
            else:
                logger.error(f"文本文件不存在或无法访问: {text_file}")
                return False

        logger.info("所有文档提取验证通过!")
        return True


def main():
    """主函数"""
    logger.info("=== 数据结构真题文档处理器启动 ===")

    processor = DocProcessor()

    # 批量处理文档
    results = processor.batch_process_docs()

    # 验证结果
    if processor.validate_extraction(results):
        logger.info("=== 文档处理任务完成 ===")
        return True
    else:
        logger.error("=== 文档处理任务失败 ===")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
