#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：查看PDF提取的文本格式
"""

import json
import re

# 读取提取的数据
with open('extracted_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 查看2014年真题的文本
pdf_2014 = data['files']['数据结构2014A.pdf']
text = pdf_2014['extraction_info']['text']

print("=== 原始文本前500字符 ===")
print(repr(text[:500]))

print("\n=== 按行分割后的前20行 ===")
lines = text.split('\n')
for i, line in enumerate(lines[:20]):
    print(f"{i:2d}: {repr(line)}")

print("\n=== 查找包含'一、'的行 ===")
for i, line in enumerate(lines):
    if '一、' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 查找包含'填空'的行 ===")
for i, line in enumerate(lines):
    if '填空' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 查找包含数字开头的行 ===")
for i, line in enumerate(lines):
    line = line.strip()
    if re.match(r'^\d+', line):
        print(f"行{i}: {repr(line)}")

print("\n=== 查找包含'1、'的行 ===")
for i, line in enumerate(lines):
    if '1、' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 查找包含'2、'的行 ===")
for i, line in enumerate(lines):
    if '2、' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 查找填空题附近的行 ===")
for i, line in enumerate(lines):
    if '填空题' in line:
        print(f"找到填空题在行{i}")
        for j in range(max(0, i-2), min(len(lines), i+10)):
            print(f"  {j:2d}: {repr(lines[j])}")
        break

print("\n=== 查找包含选择题相关的行 ===")
for i, line in enumerate(lines):
    if '选择' in line:
        print(f"行{i}: {repr(line)}")
