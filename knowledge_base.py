#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构知识点体系
为期末考试复习提供标准化的知识点分类和匹配
"""

import re
from typing import List, Dict, Tuple, Set

class KnowledgeBase:
    """数据结构知识点体系类"""
    
    def __init__(self):
        self.knowledge_categories = self._build_categories()
        self.importance_weights = self._build_weights()
    
    def _build_categories(self) -> Dict[str, Dict]:
        """构建知识点分类体系"""
        return {
            "线性表": {
                "数组": ["数组", "顺序表", "顺序存储", "下标", "索引", "随机访问"],
                "链表": ["链表", "单链表", "双链表", "循环链表", "指针", "节点", "头指针"],
                "栈": ["栈", "入栈", "出栈", "push", "pop", "栈顶", "LIFO", "后进先出"],
                "队列": ["队列", "入队", "出队", "队头", "队尾", "FIFO", "先进先出", "循环队列"]
            },
            
            "树结构": {
                "二叉树": ["二叉树", "根节点", "叶子节点", "左子树", "右子树", "父节点", "子节点"],
                "遍历": ["前序遍历", "中序遍历", "后序遍历", "层次遍历", "深度优先", "广度优先"],
                "二叉搜索树": ["二叉搜索树", "BST", "查找树", "排序二叉树"],
                "平衡树": ["平衡树", "AVL树", "红黑树", "平衡因子", "旋转"],
                "堆": ["堆", "大顶堆", "小顶堆", "最大堆", "最小堆", "堆排序"],
                "哈夫曼树": ["哈夫曼树", "Huffman", "最优二叉树", "带权路径", "编码"]
            },
            
            "图结构": {
                "图表示": ["图", "顶点", "边", "邻接矩阵", "邻接表", "有向图", "无向图"],
                "图遍历": ["深度优先搜索", "DFS", "广度优先搜索", "BFS", "连通性"],
                "最短路径": ["最短路径", "Dijkstra", "Floyd", "单源最短路径"],
                "最小生成树": ["最小生成树", "MST", "Prim", "Kruskal", "生成树"],
                "拓扑排序": ["拓扑排序", "有向无环图", "DAG", "入度", "出度"]
            },
            
            "查找算法": {
                "顺序查找": ["顺序查找", "线性查找", "遍历查找"],
                "二分查找": ["二分查找", "折半查找", "有序表查找"],
                "哈希表": ["哈希表", "散列表", "哈希函数", "冲突", "开放地址法", "链地址法"],
                "B树": ["B树", "B-tree", "B+树", "多路搜索树", "索引"]
            },
            
            "排序算法": {
                "简单排序": ["冒泡排序", "选择排序", "插入排序", "直接插入", "直接选择"],
                "高级排序": ["快速排序", "归并排序", "堆排序", "分治算法"],
                "线性排序": ["计数排序", "基数排序", "桶排序"],
                "排序分析": ["时间复杂度", "空间复杂度", "稳定性", "比较排序"]
            },
            
            "算法分析": {
                "复杂度": ["时间复杂度", "空间复杂度", "大O记号", "最坏情况", "平均情况"],
                "算法设计": ["分治算法", "贪心算法", "动态规划", "回溯算法", "递归"]
            }
        }
    
    def _build_weights(self) -> Dict[str, float]:
        """构建重要度权重"""
        return {
            # 主分类权重
            "线性表": 0.25,
            "树结构": 0.25,
            "图结构": 0.20,
            "查找算法": 0.15,
            "排序算法": 0.10,
            "算法分析": 0.05,
            
            # 子分类权重
            "数组": 1.2, "链表": 1.3, "栈": 1.1, "队列": 1.0,
            "二叉树": 1.4, "遍历": 1.3, "二叉搜索树": 1.2,
            "图表示": 1.0, "图遍历": 1.3, "最短路径": 1.1,
            "二分查找": 1.2, "哈希表": 1.3,
            "简单排序": 1.0, "高级排序": 1.4,
            "复杂度": 1.2
        }
    
    def get_knowledge_category(self, text: str) -> List[Tuple[str, str, float]]:
        """
        识别文本中的知识点
        
        Args:
            text: 待分析的文本
            
        Returns:
            List of (主分类, 子分类, 权重) tuples
        """
        results = []
        text_lower = text.lower()
        
        for main_cat, subcategories in self.knowledge_categories.items():
            for sub_cat, keywords in subcategories.items():
                score = 0
                matched_keywords = []
                
                for keyword in keywords:
                    if keyword.lower() in text_lower or keyword in text:
                        score += 1
                        matched_keywords.append(keyword)
                
                if score > 0:
                    # 计算权重
                    main_weight = self.importance_weights.get(main_cat, 0.1)
                    sub_weight = self.importance_weights.get(sub_cat, 1.0)
                    final_weight = main_weight * sub_weight * score
                    
                    results.append((main_cat, sub_cat, final_weight))
        
        # 按权重排序
        results.sort(key=lambda x: x[2], reverse=True)
        return results
    
    def get_all_categories(self) -> List[str]:
        """获取所有主分类"""
        return list(self.knowledge_categories.keys())
    
    def get_subcategories(self, main_category: str) -> List[str]:
        """获取指定主分类的所有子分类"""
        return list(self.knowledge_categories.get(main_category, {}).keys())
    
    def get_keywords(self, main_category: str, sub_category: str) -> List[str]:
        """获取指定分类的关键词"""
        return self.knowledge_categories.get(main_category, {}).get(sub_category, [])
    
    def analyze_text_knowledge(self, text: str) -> Dict:
        """分析文本的知识点分布"""
        knowledge_points = self.get_knowledge_category(text)
        
        # 统计
        main_cat_count = {}
        sub_cat_count = {}
        total_weight = 0
        
        for main_cat, sub_cat, weight in knowledge_points:
            main_cat_count[main_cat] = main_cat_count.get(main_cat, 0) + 1
            sub_cat_count[f"{main_cat}-{sub_cat}"] = weight
            total_weight += weight
        
        return {
            "knowledge_points": knowledge_points,
            "main_categories": main_cat_count,
            "subcategories": sub_cat_count,
            "total_weight": total_weight,
            "point_count": len(knowledge_points)
        }


# 全局实例
knowledge_base = KnowledgeBase()


def test_knowledge_base():
    """测试知识点体系"""
    print("=== 数据结构知识点体系测试 ===")
    
    # 测试文本
    test_texts = [
        "这道题考查二叉树的中序遍历算法",
        "使用栈来实现表达式求值",
        "图的深度优先搜索DFS算法",
        "快速排序的时间复杂度分析",
        "哈希表的冲突处理方法"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n测试{i}: {text}")
        analysis = knowledge_base.analyze_text_knowledge(text)
        
        print(f"  识别到 {analysis['point_count']} 个知识点:")
        for main_cat, sub_cat, weight in analysis['knowledge_points'][:3]:
            print(f"    {main_cat}-{sub_cat}: {weight:.2f}")
    
    # 统计信息
    print(f"\n=== 体系统计 ===")
    print(f"主分类数量: {len(knowledge_base.get_all_categories())}")
    
    total_subcats = 0
    total_keywords = 0
    for main_cat in knowledge_base.get_all_categories():
        subcats = knowledge_base.get_subcategories(main_cat)
        total_subcats += len(subcats)
        for sub_cat in subcats:
            keywords = knowledge_base.get_keywords(main_cat, sub_cat)
            total_keywords += len(keywords)
    
    print(f"子分类数量: {total_subcats}")
    print(f"关键词数量: {total_keywords}")
    
    return True


if __name__ == "__main__":
    success = test_knowledge_base()
    print(f"\n知识点体系{'构建成功' if success else '构建失败'}!")
