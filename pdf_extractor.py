#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文本提取器
用于高质量提取数据结构真题PDF文件的内容，支持结构化解析
"""

import os
import json
import logging
import sys
from typing import List, Dict, Optional, Any
from pathlib import Path
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_extraction.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PDFExtractor:
    """PDF文本提取器类"""

    def __init__(self):
        self.output_file = "extracted_data.json"
        self.pdf_files = [
            "数据结构2014A.pdf",
            "数据结构2014A答案.pdf",
            "数据结构2015A.pdf",
            "数据结构2016A.pdf",
            "数据结构2016A答案.pdf",
            "数据结构2017A.pdf",
            "数据结构2018A.pdf"
        ]
        self.extracted_data = {}

    def install_dependencies(self):
        """安装必要的依赖库"""
        try:
            import pdfplumber
            logger.info("pdfplumber已安装")
        except ImportError:
            logger.info("正在安装pdfplumber...")
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pdfplumber'])
            import pdfplumber
            logger.info("pdfplumber安装成功")

        try:
            import fitz  # PyMuPDF
            logger.info("PyMuPDF已安装")
        except ImportError:
            logger.info("正在安装PyMuPDF...")
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'PyMuPDF'])
            import fitz
            logger.info("PyMuPDF安装成功")

    def extract_pdf_text(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        提取单个PDF文件的文本内容

        Args:
            filepath: PDF文件路径

        Returns:
            包含提取内容的字典，失败时返回None
        """
        if not os.path.exists(filepath):
            logger.error(f"文件不存在: {filepath}")
            return None

        logger.info(f"开始提取PDF: {filepath}")

        # 方法1: 使用pdfplumber提取
        result = self._extract_with_pdfplumber(filepath)
        if result and result.get('text') and len(result['text'].strip()) > 100:
            logger.info(f"pdfplumber成功提取: {len(result['text'])}字符")
            return result

        # 方法2: 使用PyMuPDF提取
        result = self._extract_with_pymupdf(filepath)
        if result and result.get('text') and len(result['text'].strip()) > 100:
            logger.info(f"PyMuPDF成功提取: {len(result['text'])}字符")
            return result

        logger.error(f"所有方法都无法提取PDF内容: {filepath}")
        return None

    def _extract_with_pdfplumber(self, filepath: str) -> Optional[Dict[str, Any]]:
        """使用pdfplumber提取PDF内容"""
        try:
            import pdfplumber

            result = {
                'filepath': filepath,
                'method': 'pdfplumber',
                'text': '',
                'pages': [],
                'tables': [],
                'metadata': {}
            }

            with pdfplumber.open(filepath) as pdf:
                # 提取元数据
                if pdf.metadata:
                    result['metadata'] = {
                        'title': pdf.metadata.get('Title', ''),
                        'author': pdf.metadata.get('Author', ''),
                        'subject': pdf.metadata.get('Subject', ''),
                        'creator': pdf.metadata.get('Creator', ''),
                        'pages_count': len(pdf.pages)
                    }

                # 逐页提取文本
                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        page_data = {
                            'page_number': page_num,
                            'text': page_text.strip(),
                            'char_count': len(page_text.strip())
                        }
                        result['pages'].append(page_data)
                        result['text'] += page_text + '\n'

                        # 提取表格
                        tables = page.extract_tables()
                        if tables:
                            for table_idx, table in enumerate(tables):
                                table_data = {
                                    'page': page_num,
                                    'table_index': table_idx,
                                    'data': table
                                }
                                result['tables'].append(table_data)

            result['text'] = result['text'].strip()
            return result if result['text'] else None

        except Exception as e:
            logger.warning(f"pdfplumber提取失败: {e}")
            return None

    def _extract_with_pymupdf(self, filepath: str) -> Optional[Dict[str, Any]]:
        """使用PyMuPDF提取PDF内容"""
        try:
            import fitz

            result = {
                'filepath': filepath,
                'method': 'PyMuPDF',
                'text': '',
                'pages': [],
                'metadata': {}
            }

            doc = fitz.open(filepath)

            # 提取元数据
            metadata = doc.metadata
            if metadata:
                result['metadata'] = {
                    'title': metadata.get('title', ''),
                    'author': metadata.get('author', ''),
                    'subject': metadata.get('subject', ''),
                    'creator': metadata.get('creator', ''),
                    'pages_count': doc.page_count
                }

            # 逐页提取文本
            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text()

                if page_text:
                    page_data = {
                        'page_number': page_num + 1,
                        'text': page_text.strip(),
                        'char_count': len(page_text.strip())
                    }
                    result['pages'].append(page_data)
                    result['text'] += page_text + '\n'

            doc.close()
            result['text'] = result['text'].strip()
            return result if result['text'] else None

        except Exception as e:
            logger.warning(f"PyMuPDF提取失败: {e}")
            return None

    def clean_text(self, text: str) -> str:
        """清理提取的文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符但保留中文、英文、数字和常用标点
        text = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u00a0-\u00ff\n\r\t\(\)\[\]（）【】]', '', text)

        # 标准化换行
        text = re.sub(r'\r\n|\r', '\n', text)

        # 移除过多的连续换行
        text = re.sub(r'\n{3,}', '\n\n', text)

        return text.strip()

    def parse_content_structure(self, text: str, filepath: str) -> Dict[str, Any]:
        """解析PDF内容结构，识别题目、选项等"""
        structure = {
            'filepath': filepath,
            'questions': [],
            'sections': [],
            'total_questions': 0,
            'question_types': {}
        }

        # 清理文本并重新组织
        clean_text = self.clean_text(text)
        reorganized_text = self._reorganize_text(clean_text)

        # 按行分割
        lines = reorganized_text.split('\n')

        current_question = None
        current_section = None
        question_counter = 0

        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line or len(line) < 3:
                continue

            # 识别大题目（一、二、三、等）
            section_match = re.match(r'^-?\s*([一二三四五六七八九十])、\s*(.+)', line)
            if section_match:
                # 保存上一题
                if current_question:
                    structure['questions'].append(current_question)
                    current_question = None

                section_num = section_match.group(1)
                section_title = section_match.group(2)
                current_section = {
                    'number': section_num,
                    'title': section_title,
                    'type': self._identify_section_type(section_title)
                }
                structure['sections'].append(current_section)
                logger.info(f"识别到大题: {section_num}、{section_title}")
                continue

            # 识别小题目（数字+顿号或括号）- 更宽松的匹配
            question_patterns = [
                r'^.*?(\d+)、\s*(.+)',  # 匹配 "xxx 1、题目内容"
                r'^(\d+)[\.\)]\s*(.+)',  # 匹配 "1. 题目内容" 或 "1) 题目内容"
                r'^(\d+)\s*、\s*(.+)',   # 匹配 "1 、题目内容"
            ]

            question_matched = False
            for pattern in question_patterns:
                question_match = re.match(pattern, line)
                if question_match:
                    # 保存上一题
                    if current_question:
                        structure['questions'].append(current_question)

                    question_num = question_match.group(1)
                    question_text = question_match.group(2)
                    question_counter += 1

                    current_question = {
                        'number': int(question_num),
                        'text': question_text,
                        'options': [],
                        'type': current_section['type'] if current_section else 'unknown',
                        'section': current_section['title'] if current_section else 'unknown',
                        'line_start': line_num,
                        'raw_content': [line]
                    }
                    question_matched = True
                    break

            if question_matched:
                continue

            # 识别选择题选项（A、B、C、D）
            option_patterns = [
                r'^([ABCD])[\.\)、]\s*(.+)',  # A. 选项内容
                r'^([ABCD])\s*[\.、]\s*(.+)', # A . 选项内容
            ]

            option_matched = False
            for pattern in option_patterns:
                option_match = re.match(pattern, line)
                if option_match and current_question:
                    option_letter = option_match.group(1)
                    option_text = option_match.group(2)
                    current_question['options'].append({
                        'letter': option_letter,
                        'text': option_text
                    })
                    current_question['type'] = 'choice'
                    current_question['raw_content'].append(line)
                    option_matched = True
                    break

            if option_matched:
                continue

            # 识别填空题（包含下划线或空格）
            if current_question and ('___' in line or '_____' in line or '______' in line):
                current_question['type'] = 'fill_blank'
                current_question['raw_content'].append(line)
                continue

            # 其他内容添加到当前题目
            if current_question:
                current_question['raw_content'].append(line)
                # 根据内容进一步判断题型
                if any(keyword in line for keyword in ['算法', '设计', '实现', '分析', '证明', '递归']):
                    current_question['type'] = 'algorithm'
                elif any(keyword in line for keyword in ['简述', '说明', '解释', '比较', '构造', '写出']):
                    current_question['type'] = 'short_answer'
                elif '（' in line and '）' in line and any(letter in line for letter in ['a', 'b', 'c', 'd', 'A', 'B', 'C', 'D']):
                    current_question['type'] = 'choice'

        # 保存最后一题
        if current_question:
            structure['questions'].append(current_question)

        # 统计题型
        structure['total_questions'] = len(structure['questions'])
        for question in structure['questions']:
            qtype = question['type']
            structure['question_types'][qtype] = structure['question_types'].get(qtype, 0) + 1

        logger.info(f"解析完成: {filepath}, 识别{structure['total_questions']}道题目, {len(structure['sections'])}个大题")
        return structure

    def _reorganize_text(self, text: str) -> str:
        """重新组织文本，将分散的内容合并"""
        lines = text.split('\n')
        reorganized_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 跳过空行和短线
            if not line or line in ['-', '--', '---'] or len(line) < 2:
                i += 1
                continue

            # 如果是题目开始的模式，尝试合并后续相关内容
            if re.match(r'.*\d+、', line) or re.match(r'^[一二三四五六七八九十]、', line):
                combined_line = line
                j = i + 1

                # 向前查找相关内容进行合并
                while j < len(lines) and j < i + 5:  # 最多向前看5行
                    next_line = lines[j].strip()

                    # 如果遇到新的题目或大题，停止合并
                    if (re.match(r'.*\d+、', next_line) or
                        re.match(r'^[一二三四五六七八九十]、', next_line) or
                        re.match(r'^[ABCD][\.\)、]', next_line)):
                        break

                    # 如果是有意义的内容，合并到当前行
                    if next_line and len(next_line) > 2 and next_line not in ['-', '--', '---']:
                        combined_line += ' ' + next_line

                    j += 1

                reorganized_lines.append(combined_line)
                i = j
            else:
                reorganized_lines.append(line)
                i += 1

        return '\n'.join(reorganized_lines)

    def _identify_section_type(self, section_title: str) -> str:
        """根据大题标题识别题型"""
        if '填空' in section_title:
            return 'fill_blank'
        elif '选择' in section_title:
            return 'choice'
        elif '算法' in section_title or '设计' in section_title:
            return 'algorithm'
        elif '简答' in section_title or '问答' in section_title:
            return 'short_answer'
        elif '计算' in section_title or '求解' in section_title:
            return 'calculation'
        else:
            return 'unknown'

    def batch_extract_pdfs(self) -> Dict[str, Any]:
        """批量处理所有PDF文件"""
        logger.info("=== 开始批量提取PDF文件 ===")

        # 安装依赖
        self.install_dependencies()

        results = {
            'extraction_summary': {
                'total_files': len(self.pdf_files),
                'successful_extractions': 0,
                'failed_extractions': 0,
                'total_questions': 0
            },
            'files': {}
        }

        for pdf_file in self.pdf_files:
            if not os.path.exists(pdf_file):
                logger.warning(f"文件不存在，跳过: {pdf_file}")
                results['extraction_summary']['failed_extractions'] += 1
                continue

            # 提取PDF内容
            extracted_content = self.extract_pdf_text(pdf_file)

            if extracted_content:
                # 解析内容结构
                structure = self.parse_content_structure(
                    extracted_content['text'],
                    pdf_file
                )

                # 合并提取结果和结构分析
                file_result = {
                    'extraction_info': extracted_content,
                    'content_structure': structure,
                    'status': 'success'
                }

                results['files'][pdf_file] = file_result
                results['extraction_summary']['successful_extractions'] += 1
                results['extraction_summary']['total_questions'] += structure['total_questions']

                logger.info(f"✓ 成功处理: {pdf_file}")
            else:
                results['files'][pdf_file] = {
                    'status': 'failed',
                    'error': 'PDF内容提取失败'
                }
                results['extraction_summary']['failed_extractions'] += 1
                logger.error(f"✗ 处理失败: {pdf_file}")

        # 保存结果
        self.save_extracted_data(results)

        logger.info("=== PDF批量提取完成 ===")
        logger.info(f"成功: {results['extraction_summary']['successful_extractions']}/{results['extraction_summary']['total_files']}")
        logger.info(f"总题目数: {results['extraction_summary']['total_questions']}")

        return results

    def save_extracted_data(self, data: Dict[str, Any]):
        """保存提取的数据到JSON文件"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到: {self.output_file}")
        except Exception as e:
            logger.error(f"保存数据失败: {e}")

    def validate_extraction(self) -> bool:
        """验证提取结果的质量"""
        if not os.path.exists(self.output_file):
            logger.error(f"输出文件不存在: {self.output_file}")
            return False

        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            summary = data.get('extraction_summary', {})
            successful = summary.get('successful_extractions', 0)
            total = summary.get('total_files', 0)
            total_questions = summary.get('total_questions', 0)

            logger.info("=== 提取质量验证 ===")
            logger.info(f"文件处理成功率: {successful}/{total} ({successful/total*100:.1f}%)")
            logger.info(f"总题目数量: {total_questions}")

            # 检查每个文件的质量
            files_data = data.get('files', {})
            quality_issues = []

            for filename, file_data in files_data.items():
                if file_data.get('status') == 'success':
                    structure = file_data.get('content_structure', {})
                    questions_count = structure.get('total_questions', 0)

                    if questions_count == 0:
                        quality_issues.append(f"{filename}: 未识别到题目")
                    elif questions_count < 5:
                        quality_issues.append(f"{filename}: 题目数量较少({questions_count}道)")

                    logger.info(f"✓ {filename}: {questions_count}道题目")
                else:
                    quality_issues.append(f"{filename}: 提取失败")
                    logger.warning(f"✗ {filename}: 提取失败")

            if quality_issues:
                logger.warning("发现质量问题:")
                for issue in quality_issues:
                    logger.warning(f"  - {issue}")

            # 验证通过条件：至少50%文件成功提取，且总题目数>20
            success_rate = successful / total if total > 0 else 0
            validation_passed = success_rate >= 0.5 and total_questions >= 20

            if validation_passed:
                logger.info("✅ 提取质量验证通过")
            else:
                logger.error("❌ 提取质量验证失败")

            return validation_passed

        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            return False


def main():
    """主函数"""
    logger.info("=== PDF文本提取器启动 ===")

    extractor = PDFExtractor()

    # 批量提取PDF
    results = extractor.batch_extract_pdfs()

    # 验证提取质量
    if extractor.validate_extraction():
        logger.info("=== PDF文本提取任务完成 ===")
        return True
    else:
        logger.error("=== PDF文本提取任务失败 ===")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
