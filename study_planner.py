#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个性化复习计划生成器
基于统计分析结果生成针对期末考试的复习计划
"""

import json
import logging
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class StudySection:
    """学习章节"""
    title: str
    importance_level: str  # 高、中、低
    frequency: int
    years_appeared: List[int]
    study_time: str  # 建议学习时间
    key_points: List[str]
    typical_questions: List[str]
    memory_aids: List[str]
    exam_tips: List[str]

@dataclass
class StudyPlan:
    """复习计划"""
    duration: int  # 天数
    title: str
    overview: str
    daily_schedule: Dict[int, Dict[str, Any]]
    knowledge_sections: List[StudySection]
    general_tips: List[str]
    time_allocation: Dict[str, str]

class StudyPlanner:
    """复习计划生成器"""

    def __init__(self):
        self.statistics_data = None
        self.questions_data = None

    def load_data(self) -> bool:
        """加载统计分析数据"""
        try:
            # 加载统计报告
            with open('statistics_report.json', 'r', encoding='utf-8') as f:
                self.statistics_data = json.load(f)

            # 加载题目数据
            with open('questions_analysis.json', 'r', encoding='utf-8') as f:
                self.questions_data = json.load(f)

            logger.info("数据加载成功")
            return True
        except FileNotFoundError as e:
            logger.error(f"数据文件未找到: {e}")
            return False
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            return False

    def create_knowledge_sections(self) -> List[StudySection]:
        """按重要度组织知识点章节"""
        logger.info("开始创建知识点章节")

        sections = []
        top_knowledge_points = self.statistics_data.get('top_knowledge_points', [])

        for kp in top_knowledge_points:
            # 确定重要度等级
            importance_score = kp.get('importance_score', 0)
            if importance_score >= 65:
                importance_level = "高"
                study_time = "2-3小时"
            elif importance_score >= 50:
                importance_level = "中"
                study_time = "1-2小时"
            else:
                importance_level = "低"
                study_time = "0.5-1小时"

            # 生成学习要点
            key_points = self._generate_key_points(kp)

            # 查找典型题目
            typical_questions = self._find_typical_questions(kp)

            # 生成记忆辅助
            memory_aids = self._generate_memory_aids(kp)

            # 生成应试技巧
            exam_tips = self._create_exam_tips(kp)

            section = StudySection(
                title=f"{kp['category']}-{kp['subcategory']}",
                importance_level=importance_level,
                frequency=kp.get('frequency', 0),
                years_appeared=kp.get('years_appeared', []),
                study_time=study_time,
                key_points=key_points,
                typical_questions=typical_questions,
                memory_aids=memory_aids,
                exam_tips=exam_tips
            )

            sections.append(section)

        logger.info(f"创建了 {len(sections)} 个知识点章节")
        return sections

    def _generate_key_points(self, kp: Dict) -> List[str]:
        """生成知识点学习要点"""
        category = kp['category']
        subcategory = kp['subcategory']

        key_points_map = {
            "线性表-链表": [
                "掌握单链表、双链表、循环链表的结构特点",
                "熟练掌握链表的插入、删除、查找操作",
                "理解链表与数组的区别和适用场景",
                "掌握链表的遍历和逆置算法"
            ],
            "树结构-哈夫曼树": [
                "理解哈夫曼树的构造原理和过程",
                "掌握哈夫曼编码的生成方法",
                "理解带权路径长度的概念和计算",
                "掌握哈夫曼树在数据压缩中的应用"
            ],
            "树结构-堆": [
                "理解堆的定义和性质（大顶堆、小顶堆）",
                "掌握堆的插入和删除操作",
                "理解堆排序的原理和实现",
                "掌握堆在优先队列中的应用"
            ],
            "图结构-图表示": [
                "掌握邻接矩阵和邻接表两种存储方式",
                "理解有向图和无向图的区别",
                "掌握图的基本概念：顶点、边、度数",
                "理解稀疏图和稠密图的存储选择"
            ],
            "查找算法-哈希表": [
                "理解哈希函数的设计原则",
                "掌握冲突处理方法：开放地址法、链地址法",
                "理解装填因子对性能的影响",
                "掌握哈希表的查找、插入、删除操作"
            ],
            "排序算法-高级排序": [
                "掌握快速排序、归并排序、堆排序的原理",
                "理解分治算法的思想",
                "掌握各种排序算法的时间复杂度",
                "理解排序算法的稳定性概念"
            ]
        }

        key = f"{category}-{subcategory}"
        return key_points_map.get(key, [
            f"掌握{subcategory}的基本概念和性质",
            f"理解{subcategory}的操作方法",
            f"掌握{subcategory}的应用场景",
            f"理解{subcategory}的复杂度分析"
        ])

    def _find_typical_questions(self, kp: Dict) -> List[str]:
        """查找典型题目"""
        category = kp['category']
        subcategory = kp['subcategory']

        typical_questions = []

        # 从题目数据中查找相关题目
        for question in self.questions_data.get('questions_data', []):
            knowledge_points = question.get('knowledge_points', [])

            for knowledge_point in knowledge_points:
                if (len(knowledge_point) >= 2 and
                    knowledge_point[0] == category and
                    knowledge_point[1] == subcategory):

                    content = question.get('content', '')
                    if content and len(content) > 20:
                        # 清理和截取内容
                        clean_content = content.replace('\n', ' ').strip()
                        if len(clean_content) > 100:
                            clean_content = clean_content[:100] + "..."

                        year = question.get('year', '')
                        qtype = question.get('type', '')
                        typical_questions.append(f"({year}年{qtype}) {clean_content}")

                        if len(typical_questions) >= 3:  # 最多3个典型题目
                            break

            if len(typical_questions) >= 3:
                break

        # 如果没有找到足够的题目，添加通用示例
        if len(typical_questions) < 2:
            examples_map = {
                "线性表-链表": [
                    "设计算法删除单链表中值为x的所有节点",
                    "判断单链表是否有环的算法设计"
                ],
                "树结构-哈夫曼树": [
                    "根据给定权值构造哈夫曼树",
                    "计算哈夫曼编码的平均长度"
                ],
                "树结构-堆": [
                    "在最大堆中插入新元素的过程",
                    "堆排序算法的实现步骤"
                ]
            }

            key = f"{category}-{subcategory}"
            if key in examples_map:
                typical_questions.extend(examples_map[key])

        return typical_questions[:3]  # 返回最多3个

    def _generate_memory_aids(self, kp: Dict) -> List[str]:
        """生成记忆辅助材料"""
        category = kp['category']
        subcategory = kp['subcategory']

        memory_aids_map = {
            "线性表-链表": [
                "记忆口诀：链表插入先连后断，删除先连后释放",
                "联想记忆：链表像火车车厢，通过指针连接",
                "对比记忆：数组随机访问，链表顺序访问"
            ],
            "树结构-哈夫曼树": [
                "记忆要点：权值小的节点离根远，权值大的离根近",
                "构造步骤：选最小两个，合并成新节点，重复直到一个",
                "应用记忆：压缩编码，频率高的字符编码短"
            ],
            "树结构-堆": [
                "性质记忆：父节点总是比子节点大（大顶堆）",
                "操作记忆：插入向上调整，删除向下调整",
                "应用记忆：优先队列的最佳实现"
            ],
            "图结构-图表示": [
                "存储选择：稠密图用矩阵，稀疏图用邻接表",
                "空间复杂度：矩阵O(V²)，邻接表O(V+E)",
                "操作效率：矩阵查边快，邻接表遍历快"
            ],
            "查找算法-哈希表": [
                "冲突处理：开放地址探测，链地址法拉链",
                "性能记忆：装填因子越高，冲突越多",
                "设计原则：哈希函数要均匀分布"
            ],
            "排序算法-高级排序": [
                "时间复杂度：快归堆都是O(nlogn)",
                "稳定性记忆：归并稳定，快排堆排不稳定",
                "空间复杂度：快排O(logn)，归并O(n)，堆排O(1)"
            ]
        }

        key = f"{category}-{subcategory}"
        return memory_aids_map.get(key, [
            f"理解{subcategory}的核心概念",
            f"记住{subcategory}的关键特征",
            f"掌握{subcategory}的应用场景"
        ])

    def _create_exam_tips(self, kp: Dict) -> List[str]:
        """生成应试技巧"""
        category = kp['category']
        subcategory = kp['subcategory']

        # 基于题型分布生成技巧
        question_types = kp.get('question_types', {})
        tips = []

        if '填空题' in question_types:
            tips.append("填空题重点：记住关键概念和公式，注意术语的准确性")

        if '选择题' in question_types:
            tips.append("选择题技巧：排除法，注意题目中的关键词如'不正确'、'最佳'")

        if '简答题' in question_types:
            tips.append("简答题要点：分点作答，先写概念再举例，逻辑清晰")

        if '算法题' in question_types:
            tips.append("算法题策略：先写思路，再写伪代码，最后分析复杂度")

        if '计算题' in question_types:
            tips.append("计算题注意：步骤清晰，中间结果要写出，检查计算错误")

        # 添加通用技巧
        tips.extend([
            "时间分配：按分值分配时间，先易后难",
            "检查要点：概念准确性、计算正确性、逻辑完整性"
        ])

        return tips[:4]  # 返回最多4个技巧

    def generate_study_plan(self, days: int) -> StudyPlan:
        """生成指定天数的复习计划"""
        logger.info(f"开始生成 {days} 天复习计划")

        # 创建知识点章节
        knowledge_sections = self.create_knowledge_sections()

        # 按重要度分组
        high_priority = [s for s in knowledge_sections if s.importance_level == "高"]
        medium_priority = [s for s in knowledge_sections if s.importance_level == "中"]
        low_priority = [s for s in knowledge_sections if s.importance_level == "低"]

        # 生成每日安排
        daily_schedule = self._create_daily_schedule(days, high_priority, medium_priority, low_priority)

        # 生成时间分配建议
        time_allocation = self._create_time_allocation(days)

        # 生成通用建议
        general_tips = self._create_general_tips(days)

        plan = StudyPlan(
            duration=days,
            title=f"数据结构期末考试 {days} 天复习计划",
            overview=self._create_overview(days, len(knowledge_sections)),
            daily_schedule=daily_schedule,
            knowledge_sections=knowledge_sections,
            general_tips=general_tips,
            time_allocation=time_allocation
        )

        logger.info(f"{days} 天复习计划生成完成")
        return plan

    def _create_daily_schedule(self, days: int, high_priority: List, medium_priority: List, low_priority: List) -> Dict:
        """创建每日学习安排"""
        schedule = {}

        if days == 3:
            schedule = {
                1: {
                    "主题": "高频重点知识点",
                    "内容": [s.title for s in high_priority[:3]],
                    "目标": "掌握最重要的考点",
                    "时间": "上午3小时，下午2小时"
                },
                2: {
                    "主题": "中等重要度知识点",
                    "内容": [s.title for s in high_priority[3:]] + [s.title for s in medium_priority[:2]],
                    "目标": "理解核心概念和方法",
                    "时间": "上午3小时，下午2小时"
                },
                3: {
                    "主题": "综合复习和模拟练习",
                    "内容": ["综合练习", "查漏补缺", "模拟考试"],
                    "目标": "巩固知识，适应考试",
                    "时间": "上午2小时，下午3小时"
                }
            }
        elif days == 5:
            schedule = {
                1: {
                    "主题": "线性表和栈队列",
                    "内容": [s.title for s in high_priority if "线性表" in s.title],
                    "目标": "掌握基础数据结构",
                    "时间": "上午2小时，下午2小时"
                },
                2: {
                    "主题": "树结构重点",
                    "内容": [s.title for s in high_priority if "树结构" in s.title],
                    "目标": "理解树的各种操作",
                    "时间": "上午2小时，下午2小时"
                },
                3: {
                    "主题": "图结构和查找",
                    "内容": [s.title for s in high_priority if "图结构" in s.title or "查找" in s.title],
                    "目标": "掌握图和查找算法",
                    "时间": "上午2小时，下午2小时"
                },
                4: {
                    "主题": "排序算法和复杂度",
                    "内容": [s.title for s in medium_priority],
                    "目标": "理解排序和分析方法",
                    "时间": "上午2小时，下午2小时"
                },
                5: {
                    "主题": "综合复习和考试准备",
                    "内容": ["重点回顾", "典型题目", "模拟考试"],
                    "目标": "查漏补缺，考试准备",
                    "时间": "上午2小时，下午3小时"
                }
            }
        elif days == 7:
            all_sections = high_priority + medium_priority + low_priority
            schedule = {
                1: {
                    "主题": "线性表专题",
                    "内容": [s.title for s in all_sections if "线性表" in s.title],
                    "目标": "全面掌握线性表",
                    "时间": "上午2小时，下午1.5小时"
                },
                2: {
                    "主题": "树结构基础",
                    "内容": [s.title for s in all_sections if "树结构" in s.title][:3],
                    "目标": "理解树的基本概念",
                    "时间": "上午2小时，下午1.5小时"
                },
                3: {
                    "主题": "树结构进阶",
                    "内容": [s.title for s in all_sections if "树结构" in s.title][3:],
                    "目标": "掌握高级树结构",
                    "时间": "上午2小时，下午1.5小时"
                },
                4: {
                    "主题": "图结构专题",
                    "内容": [s.title for s in all_sections if "图结构" in s.title],
                    "目标": "理解图的存储和算法",
                    "时间": "上午2小时，下午1.5小时"
                },
                5: {
                    "主题": "查找算法",
                    "内容": [s.title for s in all_sections if "查找" in s.title],
                    "目标": "掌握各种查找方法",
                    "时间": "上午2小时，下午1.5小时"
                },
                6: {
                    "主题": "排序算法",
                    "内容": [s.title for s in all_sections if "排序" in s.title],
                    "目标": "理解排序算法原理",
                    "时间": "上午2小时，下午1.5小时"
                },
                7: {
                    "主题": "综合复习",
                    "内容": ["重点回顾", "难点突破", "模拟考试"],
                    "目标": "全面准备考试",
                    "时间": "上午2小时，下午2小时"
                }
            }

        return schedule

    def _create_time_allocation(self, days: int) -> Dict[str, str]:
        """创建时间分配建议"""
        if days == 3:
            return {
                "每日学习时间": "5-6小时",
                "高频考点": "60%",
                "中频考点": "30%",
                "综合练习": "10%"
            }
        elif days == 5:
            return {
                "每日学习时间": "4-5小时",
                "高频考点": "50%",
                "中频考点": "30%",
                "低频考点": "10%",
                "综合练习": "10%"
            }
        else:  # 7天
            return {
                "每日学习时间": "3-4小时",
                "高频考点": "40%",
                "中频考点": "30%",
                "低频考点": "20%",
                "综合练习": "10%"
            }

    def _create_general_tips(self, days: int) -> List[str]:
        """创建通用学习建议"""
        tips = [
            "📚 理论与实践结合：每学一个概念就做相关练习题",
            "🔄 多轮复习：第一轮理解概念，第二轮熟练应用，第三轮查漏补缺",
            "📝 做好笔记：整理重点概念、公式和易错点",
            "⏰ 合理安排：保证充足睡眠，避免疲劳学习",
            "🎯 重点突出：把80%的时间用在高频考点上"
        ]

        if days <= 3:
            tips.extend([
                "⚡ 时间紧迫：专注最重要的知识点，不要贪多",
                "🚀 高效学习：使用番茄工作法，25分钟专注学习"
            ])
        elif days <= 5:
            tips.extend([
                "📈 循序渐进：按计划执行，不要随意调整",
                "💪 坚持到底：中期可能会有疲劳，要坚持"
            ])
        else:
            tips.extend([
                "🎨 劳逸结合：适当休息，保持学习兴趣",
                "🔍 深入理解：有时间可以深入理解原理"
            ])

        return tips

    def _create_overview(self, days: int, total_sections: int) -> str:
        """创建计划概述"""
        return f"""
本复习计划基于2014-2018年真题分析，针对{days}天的复习时间设计。
计划覆盖{total_sections}个重要知识点，按重要度优先级安排学习顺序。
重点关注高频考点，确保在有限时间内最大化复习效果。
建议严格按照计划执行，根据个人情况微调学习时间。
        """.strip()

    def save_study_plan(self, plan: StudyPlan):
        """保存复习计划为Markdown文件"""
        filename = f"复习计划_{plan.duration}天.md"

        content = self._format_plan_to_markdown(plan)

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"复习计划已保存到 {filename}")

    def _format_plan_to_markdown(self, plan: StudyPlan) -> str:
        """将复习计划格式化为Markdown"""
        content = []

        # 标题和概述
        content.extend([
            f"# {plan.title}",
            "",
            f"*生成时间：{datetime.now().strftime('%Y年%m月%d日')}*",
            "",
            "## 📋 计划概述",
            "",
            plan.overview,
            "",
            "## ⏰ 时间分配建议",
            ""
        ])

        for key, value in plan.time_allocation.items():
            content.append(f"- **{key}**：{value}")

        content.extend(["", "## 📅 每日学习安排", ""])

        # 每日安排
        for day, schedule in plan.daily_schedule.items():
            content.extend([
                f"### 第{day}天：{schedule['主题']}",
                "",
                f"**学习目标**：{schedule['目标']}",
                "",
                f"**建议时间**：{schedule['时间']}",
                "",
                "**学习内容**：",
                ""
            ])

            for item in schedule['内容']:
                content.append(f"- {item}")

            content.extend(["", "---", ""])

        # 知识点详解
        content.extend(["## 📚 知识点详解", ""])

        for i, section in enumerate(plan.knowledge_sections, 1):
            importance_emoji = {"高": "🔥", "中": "⭐", "低": "📝"}
            emoji = importance_emoji.get(section.importance_level, "📝")

            content.extend([
                f"### {i}. {emoji} {section.title}",
                "",
                f"**重要度**：{section.importance_level} | **频率**：{section.frequency}次 | **建议时间**：{section.study_time}",
                ""
            ])

            if section.years_appeared:
                content.append(f"**出现年份**：{', '.join(map(str, section.years_appeared))}")
                content.append("")

            # 学习要点
            if section.key_points:
                content.append("#### 🎯 学习要点")
                content.append("")
                for point in section.key_points:
                    content.append(f"- {point}")
                content.append("")

            # 典型题目
            if section.typical_questions:
                content.append("#### 📝 典型题目")
                content.append("")
                for question in section.typical_questions:
                    content.append(f"- {question}")
                content.append("")

            # 记忆辅助
            if section.memory_aids:
                content.append("#### 🧠 记忆辅助")
                content.append("")
                for aid in section.memory_aids:
                    content.append(f"- {aid}")
                content.append("")

            # 应试技巧
            if section.exam_tips:
                content.append("#### 💡 应试技巧")
                content.append("")
                for tip in section.exam_tips:
                    content.append(f"- {tip}")
                content.append("")

            content.extend(["---", ""])

        # 通用建议
        content.extend(["## 💡 通用学习建议", ""])

        for tip in plan.general_tips:
            content.append(f"- {tip}")

        content.extend([
            "",
            "---",
            "",
            "## 📞 结语",
            "",
            "本复习计划基于历年真题科学分析生成，请根据个人实际情况灵活调整。",
            "祝愿考试顺利，取得理想成绩！",
            "",
            f"*本计划由数据结构真题分析系统自动生成*"
        ])

        return "\n".join(content)

    def generate_all_plans(self):
        """生成所有时长的复习计划"""
        logger.info("=== 开始生成所有复习计划 ===")

        if not self.load_data():
            logger.error("数据加载失败")
            return False

        plans = []
        for days in [3, 5, 7]:
            plan = self.generate_study_plan(days)
            self.save_study_plan(plan)
            plans.append(plan)

        logger.info("=== 所有复习计划生成完成 ===")
        return plans


def test_study_planner():
    """测试复习计划生成器"""
    print("=== 复习计划生成器测试 ===")

    planner = StudyPlanner()

    # 生成所有计划
    plans = planner.generate_all_plans()

    if plans:
        print("✅ 复习计划生成成功!")

        for plan in plans:
            print(f"📄 {plan.duration}天计划：{len(plan.knowledge_sections)}个知识点")

        return True
    else:
        print("❌ 复习计划生成失败!")
        return False


if __name__ == "__main__":
    success = test_study_planner()
    print(f"\n复习计划生成器{'测试成功' if success else '测试失败'}!")
