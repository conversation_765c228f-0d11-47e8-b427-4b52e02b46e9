#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构备考指南生成器
基于统计分析结果生成结构化的备考指南文档
"""

import os
import json
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from knowledge_system import knowledge_system

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class StudySection:
    """学习章节数据类"""
    title: str
    importance_level: str  # 高、中、低
    frequency: int
    years_appeared: List[int]
    concepts: List[str]
    key_points: List[str]
    typical_questions: List[str]
    review_suggestions: List[str]
    difficulty_tips: Dict[str, List[str]]

class StudyGuide:
    """备考指南类"""
    
    def __init__(self):
        self.title = "数据结构考试备考指南 (2014-2018年真题分析)"
        self.sections = []
        self.statistics_summary = {}
        self.general_recommendations = []
        self.study_plan = {}
    
    def add_section(self, section: StudySection):
        """添加学习章节"""
        self.sections.append(section)
    
    def set_statistics_summary(self, summary: Dict):
        """设置统计摘要"""
        self.statistics_summary = summary
    
    def add_general_recommendation(self, recommendation: str):
        """添加通用建议"""
        self.general_recommendations.append(recommendation)
    
    def set_study_plan(self, plan: Dict):
        """设置学习计划"""
        self.study_plan = plan

class ReportGenerator:
    """备考指南生成器类"""
    
    def __init__(self):
        self.statistics_dir = "statistics_output"
        self.analysis_dir = "analysis_results"
        self.output_file = "数据结构备考指南.md"
        self.knowledge_system = knowledge_system
    
    def load_statistics_data(self) -> Optional[Dict]:
        """加载统计分析数据"""
        logger.info("开始加载统计分析数据")
        
        stats_file = os.path.join(self.statistics_dir, "statistics_report.json")
        
        if not os.path.exists(stats_file):
            logger.error(f"统计报告文件不存在: {stats_file}")
            return None
        
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info("统计数据加载成功")
            return data
        except Exception as e:
            logger.error(f"加载统计数据失败: {e}")
            return None
    
    def load_analysis_data(self) -> Dict[int, List[Dict]]:
        """加载内容分析数据"""
        logger.info("开始加载内容分析数据")
        
        data = {}
        
        for year in [2014, 2015, 2016, 2017, 2018]:
            filename = f"questions_{year}.json"
            filepath = os.path.join(self.analysis_dir, filename)
            
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        questions = json.load(f)
                    data[year] = questions
                    logger.info(f"加载{year}年数据: {len(questions)}道题目")
                except Exception as e:
                    logger.error(f"加载{year}年数据失败: {e}")
        
        return data
    
    def create_knowledge_sections(self, stats_data: Dict, analysis_data: Dict) -> List[StudySection]:
        """按重要度组织知识点章节"""
        logger.info("开始创建知识点章节")
        
        sections = []
        
        # 获取所有分类的详细信息
        all_categories = self.knowledge_system.get_all_categories()
        
        for category in all_categories:
            # 从统计数据中查找该分类的信息
            category_info = stats_data.get('category_distribution', {}).get(category, {})
            
            if not category_info:
                # 如果统计数据中没有，创建基础信息
                category_info = {
                    'total_frequency': 0,
                    'avg_importance': 0,
                    'subcategories': self.knowledge_system.get_all_subcategories(category),
                    'years_coverage': []
                }
            
            # 确定重要度等级
            avg_importance = category_info.get('avg_importance', 0)
            if avg_importance >= 15:
                importance_level = "高"
            elif avg_importance >= 8:
                importance_level = "中"
            else:
                importance_level = "低"
            
            # 获取相关概念
            concepts = []
            key_points = []
            for subcategory in category_info.get('subcategories', []):
                sub_concepts = self.knowledge_system.get_subcategory_concepts(category, subcategory)
                concepts.extend(sub_concepts)
                key_points.append(f"{subcategory}的核心概念和应用")
            
            # 查找典型题目
            typical_questions = self._find_typical_questions(category, analysis_data)
            
            # 生成复习建议
            review_suggestions = self._generate_category_review_suggestions(
                category, category_info, importance_level
            )
            
            # 生成难度提示
            difficulty_tips = self._generate_difficulty_tips(category)
            
            section = StudySection(
                title=category,
                importance_level=importance_level,
                frequency=category_info.get('total_frequency', 0),
                years_appeared=category_info.get('years_coverage', []),
                concepts=list(set(concepts))[:10],  # 限制数量
                key_points=key_points,
                typical_questions=typical_questions,
                review_suggestions=review_suggestions,
                difficulty_tips=difficulty_tips
            )
            
            sections.append(section)
        
        # 按重要度和频率排序
        sections.sort(key=lambda x: (
            {"高": 3, "中": 2, "低": 1}[x.importance_level],
            x.frequency
        ), reverse=True)
        
        logger.info(f"创建了{len(sections)}个知识点章节")
        return sections
    
    def _find_typical_questions(self, category: str, analysis_data: Dict) -> List[str]:
        """查找典型题目"""
        typical_questions = []
        
        for year, questions in analysis_data.items():
            for question in questions:
                # 检查题目是否涉及该分类
                for kp in question.get('knowledge_points', []):
                    if len(kp) >= 1 and kp[0] == category:
                        # 清理题目内容
                        content = question.get('content', '')
                        if len(content) > 50 and len(content) < 200:
                            clean_content = self._clean_question_content(content)
                            if clean_content and clean_content not in typical_questions:
                                typical_questions.append(f"({year}年) {clean_content}")
                        break
                
                if len(typical_questions) >= 3:  # 每个分类最多3个典型题目
                    break
            
            if len(typical_questions) >= 3:
                break
        
        return typical_questions
    
    def _clean_question_content(self, content: str) -> str:
        """清理题目内容"""
        # 移除特殊字符和格式信息
        import re
        
        # 保留中文、英文、数字和基本标点
        cleaned = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\(\)\[\]（）【】]', '', content)
        
        # 移除过多的空白
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # 如果内容太短或包含太多乱码，返回空
        if len(cleaned) < 20 or len(re.findall(r'[^\u4e00-\u9fff\w\s\(\)\[\]（）【】]', cleaned)) > len(cleaned) * 0.3:
            return ""
        
        return cleaned[:150] + "..." if len(cleaned) > 150 else cleaned
    
    def _generate_category_review_suggestions(self, category: str, category_info: Dict, importance_level: str) -> List[str]:
        """生成分类复习建议"""
        suggestions = []
        
        # 基于重要度的建议
        if importance_level == "高":
            suggestions.append("⭐ 重点掌握：这是高频考点，需要深入理解和熟练应用")
            suggestions.append("📚 建议学习时间：3-4天，每天2-3小时")
        elif importance_level == "中":
            suggestions.append("📖 适度关注：中等重要度，需要理解基本概念和方法")
            suggestions.append("📚 建议学习时间：2-3天，每天1-2小时")
        else:
            suggestions.append("📝 了解即可：低频考点，掌握基本概念即可")
            suggestions.append("📚 建议学习时间：1-2天，每天1小时")
        
        # 基于年份覆盖的建议
        years_coverage = category_info.get('years_coverage', [])
        if len(years_coverage) >= 4:
            suggestions.append("🔄 连续多年出现，是稳定的考点，需要重点关注")
        elif len(years_coverage) >= 2:
            suggestions.append("📈 多年出现，有一定重要性，建议掌握")
        
        # 基于分类特点的建议
        category_specific_suggestions = {
            "线性表": [
                "重点掌握数组和链表的区别与应用场景",
                "熟练掌握栈和队列的操作和应用",
                "理解各种线性结构的时间复杂度"
            ],
            "树形结构": [
                "重点掌握二叉树的遍历算法",
                "理解二叉搜索树的性质和操作",
                "掌握堆的构建和调整算法"
            ],
            "图结构": [
                "掌握图的两种存储方式的优缺点",
                "熟练掌握DFS和BFS算法",
                "理解最短路径和最小生成树算法"
            ],
            "查找算法": [
                "掌握各种查找算法的适用条件",
                "重点理解哈希表的冲突处理方法",
                "掌握B树在数据库中的应用"
            ],
            "排序算法": [
                "掌握各种排序算法的时间复杂度",
                "理解稳定排序和不稳定排序的区别",
                "重点掌握快速排序和归并排序"
            ],
            "算法分析": [
                "熟练掌握时间复杂度的计算方法",
                "理解最好、最坏、平均情况的分析",
                "掌握递归算法的复杂度分析"
            ]
        }
        
        if category in category_specific_suggestions:
            suggestions.extend(category_specific_suggestions[category])
        
        return suggestions
    
    def _generate_difficulty_tips(self, category: str) -> Dict[str, List[str]]:
        """生成难度提示"""
        tips = {
            "简单": [],
            "中等": [],
            "困难": []
        }
        
        category_tips = {
            "线性表": {
                "简单": ["掌握基本概念和定义", "理解各种结构的特点"],
                "中等": ["掌握插入删除操作", "理解时间复杂度分析"],
                "困难": ["设计复杂的算法", "优化空间和时间效率"]
            },
            "树形结构": {
                "简单": ["掌握树的基本概念", "理解二叉树的性质"],
                "中等": ["掌握遍历算法", "理解平衡树的概念"],
                "困难": ["设计树的算法", "分析复杂的树结构"]
            },
            "图结构": {
                "简单": ["掌握图的基本概念", "理解存储方式"],
                "中等": ["掌握遍历算法", "理解连通性概念"],
                "困难": ["掌握最短路径算法", "理解网络流算法"]
            },
            "查找算法": {
                "简单": ["掌握顺序查找", "理解二分查找条件"],
                "中等": ["掌握哈希表原理", "理解冲突处理方法"],
                "困难": ["设计高效查找算法", "分析复杂度权衡"]
            },
            "排序算法": {
                "简单": ["掌握简单排序算法", "理解稳定性概念"],
                "中等": ["掌握高效排序算法", "理解分治思想"],
                "困难": ["设计特殊排序算法", "优化排序性能"]
            }
        }
        
        if category in category_tips:
            tips.update(category_tips[category])
        
        return tips
    
    def generate_study_plan(self, sections: List[StudySection]) -> Dict[str, Any]:
        """生成学习计划"""
        plan = {
            "总体安排": {
                "建议学习周期": "4-6周",
                "每日学习时间": "2-3小时",
                "复习轮次": "至少3轮"
            },
            "阶段安排": {}
        }
        
        # 按重要度分阶段
        high_importance = [s for s in sections if s.importance_level == "高"]
        medium_importance = [s for s in sections if s.importance_level == "中"]
        low_importance = [s for s in sections if s.importance_level == "低"]
        
        plan["阶段安排"]["第一阶段（1-2周）"] = {
            "目标": "掌握高频重点知识点",
            "内容": [s.title for s in high_importance],
            "要求": "深入理解，熟练应用"
        }
        
        plan["阶段安排"]["第二阶段（3-4周）"] = {
            "目标": "掌握中等重要度知识点",
            "内容": [s.title for s in medium_importance],
            "要求": "理解概念，掌握方法"
        }
        
        plan["阶段安排"]["第三阶段（5-6周）"] = {
            "目标": "补充低频知识点，全面复习",
            "内容": [s.title for s in low_importance] + ["综合练习", "模拟考试"],
            "要求": "查漏补缺，综合提升"
        }
        
        return plan
    
    def generate_general_recommendations(self, stats_data: Dict) -> List[str]:
        """生成通用建议"""
        recommendations = []
        
        # 基于统计数据的建议
        total_questions = stats_data.get('total_questions', 0)
        years_analyzed = stats_data.get('years_analyzed', [])
        
        recommendations.extend([
            f"📊 本指南基于{len(years_analyzed)}年共{total_questions}道真题的深度分析",
            "🎯 建议按照重要度优先级进行学习，重点突破高频考点",
            "📝 每个知识点都要结合典型题目进行练习",
            "🔄 建议进行多轮复习，第一轮理解概念，第二轮熟练应用，第三轮查漏补缺",
            "⏰ 合理安排学习时间，避免临时抱佛脚",
            "📚 结合教材和真题，理论与实践相结合",
            "🤝 建议组建学习小组，互相讨论和答疑",
            "📈 定期进行自我测试，检验学习效果"
        ])
        
        # 基于题型分布的建议
        type_dist = stats_data.get('type_analysis', {}).get('overall_distribution', {})
        if type_dist:
            most_common_type = max(type_dist.items(), key=lambda x: x[1])[0]
            recommendations.append(f"📋 {most_common_type}是最常见的题型，需要重点练习")
        
        # 基于难度分布的建议
        difficulty_dist = stats_data.get('difficulty_analysis', {}).get('overall_distribution', {})
        if difficulty_dist:
            recommendations.append("💪 注意平衡各难度层次的练习，不要只做简单题")
        
        return recommendations
    
    def format_markdown(self, guide: StudyGuide) -> str:
        """格式化为markdown文档"""
        logger.info("开始格式化markdown文档")
        
        md_content = []
        
        # 标题和目录
        md_content.extend([
            f"# {guide.title}",
            "",
            f"*生成时间：{datetime.now().strftime('%Y年%m月%d日')}*",
            "",
            "## 📋 目录",
            "",
            "1. [概述](#概述)",
            "2. [统计摘要](#统计摘要)",
            "3. [学习计划](#学习计划)",
            "4. [知识点详解](#知识点详解)",
            "5. [通用建议](#通用建议)",
            "",
            "---",
            ""
        ])
        
        # 概述
        md_content.extend([
            "## 📖 概述",
            "",
            "本备考指南基于2014-2018年数据结构真题的深度分析，通过智能算法提取知识点、",
            "统计出现频率、分析重要度趋势，为考生提供科学、系统的复习指导。",
            "",
            "### 🎯 指南特色",
            "",
            "- **数据驱动**：基于真题大数据分析，确保复习重点准确",
            "- **重点突出**：按重要度分级，帮助合理分配学习时间",
            "- **实战导向**：提供典型题目，理论与实践相结合",
            "- **个性化建议**：针对不同难度提供专门的学习策略",
            "",
            "---",
            ""
        ])
        
        # 统计摘要
        md_content.extend([
            "## 📊 统计摘要",
            "",
            f"- **分析题目总数**：{guide.statistics_summary.get('total_questions', 0)}道",
            f"- **覆盖年份**：{', '.join(map(str, guide.statistics_summary.get('years_analyzed', [])))}",
            f"- **识别知识点**：{guide.statistics_summary.get('total_knowledge_points', 0)}个",
            f"- **主要分类**：{len(guide.sections)}个",
            ""
        ])
        
        # 题型分布
        type_dist = guide.statistics_summary.get('type_analysis', {}).get('overall_distribution', {})
        if type_dist:
            md_content.append("### 📋 题型分布")
            md_content.append("")
            for qtype, count in sorted(type_dist.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / guide.statistics_summary.get('total_questions', 1)) * 100
                md_content.append(f"- **{qtype}**：{count}道 ({percentage:.1f}%)")
            md_content.append("")
        
        # 难度分布
        difficulty_dist = guide.statistics_summary.get('difficulty_analysis', {}).get('overall_distribution', {})
        if difficulty_dist:
            md_content.append("### 💪 难度分布")
            md_content.append("")
            for difficulty, count in sorted(difficulty_dist.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / guide.statistics_summary.get('total_questions', 1)) * 100
                md_content.append(f"- **{difficulty}**：{count}道 ({percentage:.1f}%)")
            md_content.append("")
        
        md_content.append("---")
        md_content.append("")
        
        # 学习计划
        md_content.extend([
            "## 📅 学习计划",
            ""
        ])
        
        if guide.study_plan:
            # 总体安排
            overall = guide.study_plan.get("总体安排", {})
            md_content.extend([
                "### 🎯 总体安排",
                "",
                f"- **建议学习周期**：{overall.get('建议学习周期', '4-6周')}",
                f"- **每日学习时间**：{overall.get('每日学习时间', '2-3小时')}",
                f"- **复习轮次**：{overall.get('复习轮次', '至少3轮')}",
                ""
            ])
            
            # 阶段安排
            stages = guide.study_plan.get("阶段安排", {})
            if stages:
                md_content.append("### 📈 阶段安排")
                md_content.append("")
                
                for stage_name, stage_info in stages.items():
                    md_content.extend([
                        f"#### {stage_name}",
                        "",
                        f"**目标**：{stage_info.get('目标', '')}",
                        "",
                        f"**要求**：{stage_info.get('要求', '')}",
                        "",
                        "**学习内容**：",
                        ""
                    ])
                    
                    for content in stage_info.get('内容', []):
                        md_content.append(f"- {content}")
                    
                    md_content.append("")
        
        md_content.append("---")
        md_content.append("")
        
        # 知识点详解
        md_content.extend([
            "## 📚 知识点详解",
            "",
            "*按重要度排序，建议优先学习高重要度内容*",
            ""
        ])
        
        for i, section in enumerate(guide.sections, 1):
            # 重要度标记
            importance_emoji = {"高": "🔥", "中": "⭐", "低": "📝"}
            emoji = importance_emoji.get(section.importance_level, "📝")
            
            md_content.extend([
                f"### {i}. {emoji} {section.title}",
                "",
                f"**重要度**：{section.importance_level} | **出现频率**：{section.frequency}次",
                ""
            ])
            
            if section.years_appeared:
                md_content.append(f"**出现年份**：{', '.join(map(str, section.years_appeared))}")
                md_content.append("")
            
            # 核心概念
            if section.concepts:
                md_content.append("#### 🧠 核心概念")
                md_content.append("")
                for concept in section.concepts[:5]:  # 限制显示数量
                    md_content.append(f"- {concept}")
                md_content.append("")
            
            # 学习要点
            if section.key_points:
                md_content.append("#### 🎯 学习要点")
                md_content.append("")
                for point in section.key_points:
                    md_content.append(f"- {point}")
                md_content.append("")
            
            # 典型题目
            if section.typical_questions:
                md_content.append("#### 📝 典型题目")
                md_content.append("")
                for question in section.typical_questions:
                    md_content.append(f"- {question}")
                md_content.append("")
            
            # 复习建议
            if section.review_suggestions:
                md_content.append("#### 💡 复习建议")
                md_content.append("")
                for suggestion in section.review_suggestions:
                    md_content.append(f"- {suggestion}")
                md_content.append("")
            
            # 难度提示
            if section.difficulty_tips:
                md_content.append("#### 🎚️ 难度提示")
                md_content.append("")
                for difficulty, tips in section.difficulty_tips.items():
                    if tips:
                        md_content.append(f"**{difficulty}题**：")
                        for tip in tips:
                            md_content.append(f"- {tip}")
                        md_content.append("")
            
            md_content.append("---")
            md_content.append("")
        
        # 通用建议
        md_content.extend([
            "## 💡 通用建议",
            ""
        ])
        
        for recommendation in guide.general_recommendations:
            md_content.append(f"- {recommendation}")
        
        md_content.extend([
            "",
            "---",
            "",
            "## 📞 结语",
            "",
            "本指南基于历年真题的科学分析，旨在帮助考生高效备考。建议结合个人实际情况，",
            "灵活调整学习计划。祝愿所有考生都能取得理想成绩！",
            "",
            "*如有疑问或建议，欢迎反馈交流。*",
            "",
            f"---",
            f"*本指南由数据结构真题分析系统自动生成*"
        ])
        
        return "\n".join(md_content)
    
    def generate_study_guide(self) -> bool:
        """生成完整的备考指南"""
        logger.info("=== 开始生成备考指南 ===")
        
        # 加载数据
        stats_data = self.load_statistics_data()
        if not stats_data:
            logger.error("无法加载统计数据")
            return False
        
        analysis_data = self.load_analysis_data()
        if not analysis_data:
            logger.error("无法加载分析数据")
            return False
        
        # 创建指南对象
        guide = StudyGuide()
        guide.set_statistics_summary(stats_data)
        
        # 创建知识点章节
        sections = self.create_knowledge_sections(stats_data, analysis_data)
        for section in sections:
            guide.add_section(section)
        
        # 生成学习计划
        study_plan = self.generate_study_plan(sections)
        guide.set_study_plan(study_plan)
        
        # 生成通用建议
        recommendations = self.generate_general_recommendations(stats_data)
        for rec in recommendations:
            guide.add_general_recommendation(rec)
        
        # 格式化为markdown
        markdown_content = self.format_markdown(guide)
        
        # 保存文件
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"备考指南已保存: {self.output_file}")
            return True
        except Exception as e:
            logger.error(f"保存备考指南失败: {e}")
            return False


def test_report_generator():
    """测试备考指南生成器"""
    print("=== 备考指南生成器测试 ===")
    
    generator = ReportGenerator()
    
    # 生成备考指南
    success = generator.generate_study_guide()
    
    if success:
        print("✅ 备考指南生成成功!")
        
        # 检查文件是否存在
        if os.path.exists(generator.output_file):
            with open(generator.output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 文件大小: {len(content)}字符")
            print(f"📝 文件路径: {generator.output_file}")
            
            # 显示前几行内容
            lines = content.split('\n')
            print(f"\n📖 内容预览:")
            for i, line in enumerate(lines[:10], 1):
                print(f"  {i:2d}: {line}")
            
            if len(lines) > 10:
                print(f"  ... (共{len(lines)}行)")
        
        return True
    else:
        print("❌ 备考指南生成失败!")
        return False


if __name__ == "__main__":
    success = test_report_generator()
    print(f"\n备考指南生成器{'测试成功' if success else '测试失败'}!")
