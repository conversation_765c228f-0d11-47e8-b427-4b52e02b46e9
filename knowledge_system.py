#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构知识点分类体系
基于严蔚敏《数据结构》教材建立的标准化知识点分类和标签系统
"""

import re
from typing import List, Dict, Set, Tuple, Optional
from collections import defaultdict

class KnowledgeSystem:
    """数据结构知识点分类体系类"""

    def __init__(self):
        self.knowledge_categories = self._build_knowledge_categories()
        self.importance_weights = self._build_importance_weights()
        self.keyword_patterns = self._build_keyword_patterns()

    def _build_knowledge_categories(self) -> Dict[str, Dict]:
        """构建知识点分类体系"""
        return {
            "线性表": {
                "description": "线性数据结构，包括数组、链表等",
                "subcategories": {
                    "数组": {
                        "keywords": ["数组", "array", "顺序表", "顺序存储", "下标", "索引", "一维数组", "二维数组", "多维数组"],
                        "concepts": ["随机访问", "顺序存储", "内存连续", "下标计算", "数组越界"]
                    },
                    "链表": {
                        "keywords": ["链表", "linked list", "单链表", "双链表", "循环链表", "指针", "节点", "头指针", "头结点"],
                        "concepts": ["动态存储", "指针操作", "插入删除", "遍历", "链式存储"]
                    },
                    "栈": {
                        "keywords": ["栈", "stack", "入栈", "出栈", "push", "pop", "栈顶", "栈底", "LIFO", "后进先出"],
                        "concepts": ["递归", "表达式求值", "括号匹配", "函数调用", "栈溢出"]
                    },
                    "队列": {
                        "keywords": ["队列", "queue", "入队", "出队", "队头", "队尾", "FIFO", "先进先出", "循环队列", "优先队列"],
                        "concepts": ["缓冲区", "调度", "广度优先搜索", "队列满", "队列空"]
                    }
                }
            },

            "树形结构": {
                "description": "树状层次数据结构",
                "subcategories": {
                    "二叉树": {
                        "keywords": ["二叉树", "binary tree", "根节点", "叶子节点", "左子树", "右子树", "父节点", "子节点", "兄弟节点"],
                        "concepts": ["树的遍历", "前序遍历", "中序遍历", "后序遍历", "层次遍历", "树的深度", "树的高度"]
                    },
                    "二叉搜索树": {
                        "keywords": ["二叉搜索树", "BST", "binary search tree", "查找树", "排序二叉树"],
                        "concepts": ["中序遍历有序", "查找", "插入", "删除", "最大值", "最小值"]
                    },
                    "平衡树": {
                        "keywords": ["平衡树", "AVL树", "红黑树", "平衡因子", "旋转", "左旋", "右旋"],
                        "concepts": ["平衡性", "时间复杂度", "自平衡", "树的重构"]
                    },
                    "堆": {
                        "keywords": ["堆", "heap", "大顶堆", "小顶堆", "最大堆", "最小堆", "堆排序", "优先队列"],
                        "concepts": ["完全二叉树", "堆化", "上浮", "下沉", "堆的调整"]
                    },
                    "哈夫曼树": {
                        "keywords": ["哈夫曼树", "Huffman", "最优二叉树", "带权路径长度", "编码"],
                        "concepts": ["贪心算法", "数据压缩", "前缀编码", "权值"]
                    }
                }
            },

            "图结构": {
                "description": "图论相关数据结构和算法",
                "subcategories": {
                    "图的表示": {
                        "keywords": ["图", "graph", "顶点", "边", "邻接矩阵", "邻接表", "有向图", "无向图", "权重", "度"],
                        "concepts": ["图的存储", "空间复杂度", "稀疏图", "稠密图"]
                    },
                    "图的遍历": {
                        "keywords": ["深度优先搜索", "DFS", "广度优先搜索", "BFS", "遍历", "访问标记"],
                        "concepts": ["连通性", "路径", "环检测", "拓扑排序"]
                    },
                    "最短路径": {
                        "keywords": ["最短路径", "Dijkstra", "Floyd", "Bellman-Ford", "单源最短路径", "所有顶点对最短路径"],
                        "concepts": ["松弛操作", "负权边", "路径长度", "距离矩阵"]
                    },
                    "最小生成树": {
                        "keywords": ["最小生成树", "MST", "Prim", "Kruskal", "生成树", "连通图"],
                        "concepts": ["贪心算法", "并查集", "权重最小", "无环连通"]
                    },
                    "拓扑排序": {
                        "keywords": ["拓扑排序", "topological sort", "有向无环图", "DAG", "入度", "出度"],
                        "concepts": ["偏序关系", "依赖关系", "课程安排", "编译顺序"]
                    }
                }
            },

            "查找算法": {
                "description": "各种查找方法和数据结构",
                "subcategories": {
                    "顺序查找": {
                        "keywords": ["顺序查找", "线性查找", "sequential search", "遍历查找"],
                        "concepts": ["时间复杂度O(n)", "适用于无序表", "简单实现"]
                    },
                    "二分查找": {
                        "keywords": ["二分查找", "折半查找", "binary search", "有序表查找"],
                        "concepts": ["时间复杂度O(log n)", "有序前提", "中间位置", "递归实现"]
                    },
                    "哈希表": {
                        "keywords": ["哈希表", "hash table", "散列表", "哈希函数", "冲突", "开放地址法", "链地址法"],
                        "concepts": ["平均时间复杂度O(1)", "装填因子", "冲突解决", "哈希值"]
                    },
                    "B树": {
                        "keywords": ["B树", "B-tree", "B+树", "多路搜索树", "外存储器", "磁盘存储"],
                        "concepts": ["多路平衡", "页面存储", "数据库索引", "减少磁盘访问"]
                    }
                }
            },

            "排序算法": {
                "description": "各种排序方法和算法分析",
                "subcategories": {
                    "简单排序": {
                        "keywords": ["冒泡排序", "选择排序", "插入排序", "bubble sort", "selection sort", "insertion sort"],
                        "concepts": ["时间复杂度O(n²)", "稳定性", "原地排序", "比较排序"]
                    },
                    "高级排序": {
                        "keywords": ["快速排序", "归并排序", "堆排序", "quick sort", "merge sort", "heap sort"],
                        "concepts": ["分治算法", "时间复杂度O(n log n)", "递归实现", "空间复杂度"]
                    },
                    "线性排序": {
                        "keywords": ["计数排序", "基数排序", "桶排序", "counting sort", "radix sort", "bucket sort"],
                        "concepts": ["非比较排序", "线性时间", "额外空间", "特定条件"]
                    },
                    "外部排序": {
                        "keywords": ["外部排序", "external sort", "多路归并", "磁盘排序", "大文件排序"],
                        "concepts": ["内存限制", "磁盘I/O", "多路归并", "临时文件"]
                    }
                }
            },

            "算法分析": {
                "description": "算法复杂度分析和设计方法",
                "subcategories": {
                    "时间复杂度": {
                        "keywords": ["时间复杂度", "time complexity", "大O记号", "最坏情况", "平均情况", "最好情况"],
                        "concepts": ["渐近分析", "增长率", "常数时间", "对数时间", "线性时间", "平方时间"]
                    },
                    "空间复杂度": {
                        "keywords": ["空间复杂度", "space complexity", "辅助空间", "原地算法", "内存使用"],
                        "concepts": ["存储需求", "递归栈", "动态分配", "空间换时间"]
                    },
                    "算法设计": {
                        "keywords": ["分治算法", "贪心算法", "动态规划", "回溯算法", "递归", "迭代"],
                        "concepts": ["问题分解", "最优子结构", "重叠子问题", "状态转移"]
                    }
                }
            },

            "应用实例": {
                "description": "数据结构的实际应用场景",
                "subcategories": {
                    "表达式处理": {
                        "keywords": ["表达式求值", "中缀表达式", "后缀表达式", "前缀表达式", "运算符优先级"],
                        "concepts": ["栈的应用", "语法分析", "编译原理", "计算器实现"]
                    },
                    "字符串处理": {
                        "keywords": ["字符串匹配", "KMP算法", "模式匹配", "字符串查找"],
                        "concepts": ["部分匹配表", "失效函数", "文本处理", "搜索引擎"]
                    },
                    "系统应用": {
                        "keywords": ["操作系统", "数据库", "编译器", "网络协议", "文件系统"],
                        "concepts": ["进程调度", "内存管理", "索引结构", "缓存机制"]
                    }
                }
            }
        }

    def _build_importance_weights(self) -> Dict[str, float]:
        """构建知识点重要度权重表"""
        return {
            # 一级分类权重
            "线性表": 0.25,
            "树形结构": 0.20,
            "图结构": 0.15,
            "查找算法": 0.15,
            "排序算法": 0.15,
            "算法分析": 0.05,
            "应用实例": 0.05,

            # 二级分类权重调整因子
            "数组": 1.2,
            "链表": 1.3,
            "栈": 1.1,
            "队列": 1.0,
            "二叉树": 1.4,
            "二叉搜索树": 1.2,
            "平衡树": 0.8,
            "堆": 1.1,
            "哈夫曼树": 0.7,
            "图的表示": 1.0,
            "图的遍历": 1.3,
            "最短路径": 1.1,
            "最小生成树": 1.0,
            "拓扑排序": 0.8,
            "顺序查找": 0.8,
            "二分查找": 1.2,
            "哈希表": 1.3,
            "B树": 0.9,
            "简单排序": 1.1,
            "高级排序": 1.4,
            "线性排序": 0.8,
            "外部排序": 0.6,
            "时间复杂度": 1.2,
            "空间复杂度": 0.9,
            "算法设计": 1.0,
            "表达式处理": 1.0,
            "字符串处理": 0.8,
            "系统应用": 0.7
        }

    def _build_keyword_patterns(self) -> Dict[str, List]:
        """构建关键词正则表达式模式"""
        patterns = {}

        for category, cat_info in self.knowledge_categories.items():
            patterns[category] = []
            for subcategory, sub_info in cat_info["subcategories"].items():
                # 为每个关键词创建正则表达式模式
                for keyword in sub_info["keywords"]:
                    # 对于中文关键词，不使用词边界，对于英文使用词边界
                    if re.search(r'[\u4e00-\u9fff]', keyword):
                        # 中文关键词
                        pattern = re.compile(keyword, re.IGNORECASE)
                    else:
                        # 英文关键词，使用词边界
                        pattern = re.compile(r'\b' + re.escape(keyword) + r'\b', re.IGNORECASE)
                    patterns[category].append((pattern, subcategory, keyword))

        return patterns

    def get_knowledge_category(self, text: str) -> List[Tuple[str, str, str, int]]:
        """
        识别文本中的知识点

        Args:
            text: 待分析的文本

        Returns:
            List of (category, subcategory, keyword, count) tuples
        """
        results = []

        for category, patterns in self.keyword_patterns.items():
            for pattern, subcategory, keyword in patterns:
                matches = pattern.findall(text)
                if matches:
                    count = len(matches)
                    results.append((category, subcategory, keyword, count))

        return results

    def analyze_text_knowledge_points(self, text: str) -> Dict[str, Dict]:
        """
        分析文本中的知识点分布

        Args:
            text: 待分析的文本

        Returns:
            知识点分析结果字典
        """
        knowledge_points = self.get_knowledge_category(text)

        # 统计各类别的出现次数
        category_stats = defaultdict(int)
        subcategory_stats = defaultdict(int)
        keyword_stats = defaultdict(int)

        for category, subcategory, keyword, count in knowledge_points:
            category_stats[category] += count
            subcategory_stats[f"{category}-{subcategory}"] += count
            keyword_stats[keyword] += count

        # 计算重要度得分
        importance_scores = {}
        for category, count in category_stats.items():
            base_weight = self.importance_weights.get(category, 0.1)
            importance_scores[category] = count * base_weight

        return {
            "knowledge_points": knowledge_points,
            "category_stats": dict(category_stats),
            "subcategory_stats": dict(subcategory_stats),
            "keyword_stats": dict(keyword_stats),
            "importance_scores": importance_scores,
            "total_keywords": len(knowledge_points),
            "unique_categories": len(category_stats),
            "unique_subcategories": len(subcategory_stats)
        }

    def get_category_description(self, category: str) -> str:
        """获取分类描述"""
        return self.knowledge_categories.get(category, {}).get("description", "未知分类")

    def get_subcategory_concepts(self, category: str, subcategory: str) -> List[str]:
        """获取子分类的相关概念"""
        cat_info = self.knowledge_categories.get(category, {})
        sub_info = cat_info.get("subcategories", {}).get(subcategory, {})
        return sub_info.get("concepts", [])

    def get_all_categories(self) -> List[str]:
        """获取所有一级分类"""
        return list(self.knowledge_categories.keys())

    def get_all_subcategories(self, category: str = None) -> List[str]:
        """获取所有二级分类"""
        if category:
            cat_info = self.knowledge_categories.get(category, {})
            return list(cat_info.get("subcategories", {}).keys())
        else:
            all_subcategories = []
            for cat_info in self.knowledge_categories.values():
                all_subcategories.extend(cat_info.get("subcategories", {}).keys())
            return all_subcategories

    def validate_system(self) -> Dict[str, bool]:
        """验证知识点分类体系的完整性"""
        validation_results = {
            "has_all_major_categories": True,
            "has_sufficient_keywords": True,
            "has_importance_weights": True,
            "patterns_compiled": True
        }

        # 检查主要分类是否完整
        required_categories = ["线性表", "树形结构", "图结构", "查找算法", "排序算法"]
        for category in required_categories:
            if category not in self.knowledge_categories:
                validation_results["has_all_major_categories"] = False
                break

        # 检查关键词数量
        total_keywords = 0
        for cat_info in self.knowledge_categories.values():
            for sub_info in cat_info.get("subcategories", {}).values():
                total_keywords += len(sub_info.get("keywords", []))

        if total_keywords < 100:  # 至少应该有100个关键词
            validation_results["has_sufficient_keywords"] = False

        # 检查重要度权重
        for category in self.knowledge_categories.keys():
            if category not in self.importance_weights:
                validation_results["has_importance_weights"] = False
                break

        # 检查正则表达式模式
        try:
            for patterns in self.keyword_patterns.values():
                for pattern, _, _ in patterns:
                    pattern.pattern  # 测试模式是否有效
        except Exception:
            validation_results["patterns_compiled"] = False

        return validation_results


# 全局实例
knowledge_system = KnowledgeSystem()


def test_knowledge_system():
    """测试知识点分类体系"""
    print("=== 数据结构知识点分类体系测试 ===")

    # 测试文本
    test_text = """
    这道题考查的是二叉树的遍历算法。给定一个二叉搜索树，要求实现中序遍历。
    中序遍历的时间复杂度是O(n)，空间复杂度是O(h)，其中h是树的高度。
    在二叉搜索树中，中序遍历的结果是有序的。
    另外还涉及到栈的应用，因为递归实现需要使用系统栈。
    """

    # 分析知识点
    analysis = knowledge_system.analyze_text_knowledge_points(test_text)

    print(f"检测到的知识点数量: {analysis['total_keywords']}")
    print(f"涉及的分类数量: {analysis['unique_categories']}")
    print(f"涉及的子分类数量: {analysis['unique_subcategories']}")

    print("\n分类统计:")
    for category, count in analysis['category_stats'].items():
        print(f"  {category}: {count}次")

    print("\n重要度得分:")
    for category, score in analysis['importance_scores'].items():
        print(f"  {category}: {score:.2f}")

    # 验证系统完整性
    validation = knowledge_system.validate_system()
    print(f"\n系统验证结果:")
    for check, result in validation.items():
        status = "✓" if result else "✗"
        print(f"  {check}: {status}")

    return all(validation.values())


if __name__ == "__main__":
    success = test_knowledge_system()
    print(f"\n知识点分类体系{'构建成功' if success else '构建失败'}!")
