#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重组后的文本
"""

import json
import re

def clean_text(text):
    """清理提取的文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除特殊字符但保留中文、英文、数字和常用标点
    text = re.sub(r'[^\u4e00-\u9fff\u0020-\u007e\u00a0-\u00ff\n\r\t\(\)\[\]（）【】]', '', text)
    
    # 标准化换行
    text = re.sub(r'\r\n|\r', '\n', text)
    
    # 移除过多的连续换行
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    return text.strip()

def reorganize_text(text):
    """重新组织文本，将分散的内容合并"""
    lines = text.split('\n')
    reorganized_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行和短线
        if not line or line in ['-', '--', '---'] or len(line) < 2:
            i += 1
            continue
        
        # 如果是题目开始的模式，尝试合并后续相关内容
        if re.match(r'.*\d+、', line) or re.match(r'^[一二三四五六七八九十]、', line):
            combined_line = line
            j = i + 1
            
            # 向前查找相关内容进行合并
            while j < len(lines) and j < i + 5:  # 最多向前看5行
                next_line = lines[j].strip()
                
                # 如果遇到新的题目或大题，停止合并
                if (re.match(r'.*\d+、', next_line) or 
                    re.match(r'^[一二三四五六七八九十]、', next_line) or
                    re.match(r'^[ABCD][\.\)、]', next_line)):
                    break
                
                # 如果是有意义的内容，合并到当前行
                if next_line and len(next_line) > 2 and next_line not in ['-', '--', '---']:
                    combined_line += ' ' + next_line
                
                j += 1
            
            reorganized_lines.append(combined_line)
            i = j
        else:
            reorganized_lines.append(line)
            i += 1
    
    return '\n'.join(reorganized_lines)

# 读取提取的数据
with open('extracted_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 查看2014年真题的文本
pdf_2014 = data['files']['数据结构2014A.pdf']
text = pdf_2014['extraction_info']['text']

print("=== 原始文本 ===")
print(text[:1000])

print("\n=== 清理后的文本 ===")
clean = clean_text(text)
print(clean[:1000])

print("\n=== 重组后的文本 ===")
reorganized = reorganize_text(clean)
print(reorganized[:1000])

print("\n=== 重组后的行（前30行）===")
lines = reorganized.split('\n')
for i, line in enumerate(lines[:30]):
    print(f"{i:2d}: {repr(line)}")

print("\n=== 查找包含'一、'的行 ===")
for i, line in enumerate(lines):
    if '一、' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 查找包含'1、'的行 ===")
for i, line in enumerate(lines):
    if '1、' in line:
        print(f"行{i}: {repr(line)}")

print("\n=== 测试正则表达式匹配 ===")
test_patterns = [
    r'^-?\s*([一二三四五六七八九十])、\s*(.+)',
    r'^.*?(\d+)、\s*(.+)',
    r'^(\d+)[\.\)]\s*(.+)',
    r'^(\d+)\s*、\s*(.+)',
]

for i, line in enumerate(lines[:20]):
    line = line.strip()
    if not line or len(line) < 3:
        continue
    
    print(f"\n测试行{i}: {repr(line)}")
    for j, pattern in enumerate(test_patterns):
        match = re.match(pattern, line)
        if match:
            print(f"  模式{j+1}匹配: {match.groups()}")
