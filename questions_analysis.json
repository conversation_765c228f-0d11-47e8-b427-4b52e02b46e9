{"total_questions": 64, "by_year": {"2014": 19, "2015": 9, "2016": 19, "2017": 8, "2018": 9}, "by_type": {"填空题": 27, "计算题": 13, "简答题": 15, "选择题": 7, "算法题": 2}, "by_difficulty": {"简单": 53, "中等": 8, "困难": 3}, "by_section": {"第九题": 4, "第二题": 11, "第三题": 2, "第一题": 23, "第六题": 2, "第七题": 3, "填空题": 3, "第四题": 1, "第八题": 4, "简答题": 10, "选择题": 1}, "knowledge_distribution": {"树结构-堆": {"count": 7, "total_weight": 3.5}, "排序算法-高级排序": {"count": 9, "total_weight": 1.8199999999999996}, "排序算法-线性排序": {"count": 3, "total_weight": 0.30000000000000004}, "树结构-哈夫曼树": {"count": 7, "total_weight": 3.25}, "查找算法-哈希表": {"count": 6, "total_weight": 2.7300000000000004}, "图结构-图表示": {"count": 10, "total_weight": 3.2000000000000006}, "树结构-遍历": {"count": 5, "total_weight": 1.625}, "线性表-链表": {"count": 6, "total_weight": 3.25}, "树结构-二叉树": {"count": 1, "total_weight": 0.35}, "排序算法-简单排序": {"count": 2, "total_weight": 0.7000000000000001}, "算法分析-复杂度": {"count": 1, "total_weight": 0.12}, "排序算法-排序分析": {"count": 1, "total_weight": 0.1}, "线性表-数组": {"count": 1, "total_weight": 0.3}}, "questions_data": [{"id": "2014_数据结构2014A.pdf_第九题_1", "type": "填空题", "content": "总分 教 场 考 得分 - - - - - - 燕 - - - - - - - - -", "options": [], "section": "第九题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "总分 教  场  考 得分 - - - - - -    燕 - - - - - - - - -"}, {"id": "2014_数据结构2014A.pdf_第二题_1", "type": "填空题", "content": "叉树__中序____遍历序列相同。 业 专- - - - - - - - -", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "叉树__中序____遍历序列相同。 业 专- - - - - - - - -"}, {"id": "2014_数据结构2014A.pdf_第三题_1", "type": "填空题", "content": "趟:13,20,21,25,27,35,46,68,84 A．基数排序 B．快速排序 C．堆排序 D． 起泡排序", "options": [], "section": "第三题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.27999999999999997], ["排序算法", "线性排序", 0.1]], "difficulty": "简单", "year": 2014, "raw_text": "趟:13,20,21,25,27,35,46,68,84 A．基数排序 B．快速排序 C．堆排序 D． 起泡排序"}, {"id": "2014_数据结构2014A.pdf_第一题_1", "type": "计算题", "content": "棵哈曼夫树，并计算该哈曼 夫树的带权路径长度WPL。", "options": [], "section": "第一题", "knowledge_points": [["树结构", "哈夫曼树", 0.25]], "difficulty": "简单", "year": 2014, "raw_text": "棵哈曼夫树，并计算该哈曼 夫树的带权路径长度WPL。"}, {"id": "2014_数据结构2014A.pdf_第二题_1", "type": "填空题", "content": "叉排序树的步骤。 - - - - - - 号 : - - - - - -", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "叉排序树的步骤。  - - - - - - 号 : - - - - - -"}, {"id": "2014_数据结构2014A.pdf_第六题_1", "type": "简答题", "content": "(10 分)设哈希函数 H(k)=K mod 7, 哈希表的地址空间为 0～6，对关键字序列 座 {32,13,49,18,22,38,21}按链地址法处理冲突的办法构造哈希表，并指出查找关键字 21 - - - - - - - - - - - - 需 要进行几次比较。 : - - - - - -", "options": [], "section": "第六题", "knowledge_points": [["查找算法", "哈希表", 0.78]], "difficulty": "中等", "year": 2014, "raw_text": "(10 分)设哈希函数 H(k)=K mod 7, 哈希表的地址空间为 0～6，对关键字序列 座   {32,13,49,18,22,38,21}按链地址法处理冲突的办法构造哈希表，并指出查找关键字 21  - - - - - - - - - - - - 需 要进行几次比较。  : - - - - - -"}, {"id": "2014_数据结构2014A.pdf_第七题_1", "type": "计算题", "content": "写出其邻接表结构； 教 线线线", "options": [], "section": "第七题", "knowledge_points": [["图结构", "图表示", 0.2]], "difficulty": "简单", "year": 2014, "raw_text": "写出其邻接表结构； 教 线线线"}, {"id": "2014_数据结构2014A.pdf_第七题_2", "type": "计算题", "content": "根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列； 场 考- - - - - -", "options": [], "section": "第七题", "knowledge_points": [["图结构", "图表示", 0.4], ["树结构", "遍历", 0.325]], "difficulty": "简单", "year": 2014, "raw_text": "根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列； 场 考- - - - - -"}, {"id": "2014_数据结构2014A.pdf_第一题_1", "type": "选择题", "content": "个带头结点的单链表 A 分解为两个带头结点的单链表", "options": ["B. C，其  中B表中的结点为A表值小于0的结点，而C表中的结点为A表值大于等于0的结点(链 - - - - - - - - - 表A的元素类型为整型，要求B、C表利用A表的结点进行存储)"], "section": "第一题", "knowledge_points": [["线性表", "链表", 0.65]], "difficulty": "简单", "year": 2014, "raw_text": "个带头结点的单链表 A 分解为两个带头结点的单链表 B、C，其  中B表中的结点为A表值小于0的结点，而C表中的结点为A表值大于等于0的结点(链 - - - - - - - - - 表A的元素类型为整型，要求B、C表利用A表的结点进行存储)"}, {"id": "2014_数据结构2014A.pdf_第二题_1", "type": "填空题", "content": "叉树中度为2的结点数目。 年 业 专- - - - - - - - - - - - - - - - - - ＋ 第3页 共3页 ＋ - - - - - - - - - - - - : 装装装 名 姓 - - - - - - - - - - - - - - - - - - 号 : 学", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "中等", "year": 2014, "raw_text": "叉树中度为2的结点数目。  年 业 专- - - - - - - - -    - - - - - - - - - ＋ 第3页 共3页 ＋    - - - - - - - - - - - - : 装装装 名  姓  - - - - - - - - -    - - - - - - - - -    号 : 学"}, {"id": "2014_数据结构2014A答案.pdf_第一题_1", "type": "选择题", "content": "学期数据结构(", "options": ["A<PERSON> 卷试题答案"], "section": "第一题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "学期数据结构(A)卷试题答案"}, {"id": "2014_数据结构2014A答案.pdf_填空题_4", "type": "填空题", "content": "((b,c),d,e)", "options": [], "section": "填空题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "((b,c),d,e)"}, {"id": "2014_数据结构2014A答案.pdf_填空题_7", "type": "填空题", "content": "算法中基本操作重复执行的次数", "options": [], "section": "填空题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "算法中基本操作重复执行的次数"}, {"id": "2014_数据结构2014A答案.pdf_第四题_1", "type": "计算题", "content": "(6分)所构造的哈夫曼树为： 21 12 9 6 6 4 5 3 3 1 2 带权路径长度WPL =3*3+(1+2)*4+2*6+(4+5)*2=51", "options": [], "section": "第四题", "knowledge_points": [["树结构", "哈夫曼树", 0.5]], "difficulty": "简单", "year": 2014, "raw_text": "(6分)所构造的哈夫曼树为： 21 12 9 6 6 4 5 3 3 1 2 带权路径长度WPL =3*3+(1+2)*4+2*6+(4+5)*2=51"}, {"id": "2014_数据结构2014A答案.pdf_第二题_1", "type": "填空题", "content": "叉树且左右子树的深度之 差绝对值不超过1.", "options": [], "section": "第二题", "knowledge_points": [["树结构", "二叉树", 0.35]], "difficulty": "简单", "year": 2014, "raw_text": "叉树且左右子树的深度之 差绝对值不超过1."}, {"id": "2014_数据结构2014A答案.pdf_第六题_1", "type": "简答题", "content": "(10分) 查找关键字21需要比较2次。 0 49 21 1 22 2 3 38 4 32 18 5 6 13", "options": [], "section": "第六题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "(10分) 查找关键字21需要比较2次。 0 49 21 1 22 2 3 38 4 32 18 5 6 13"}, {"id": "2014_数据结构2014A答案.pdf_第七题_1", "type": "填空题", "content": "(12分) (1)该有向图的邻接表为 0 a 1 10 3 30 4 100 b 2 50 1 2 c 4 10 d 2 20 3 4 e 3 60 步骤 i=1 i=2 i=3 i=4 节点 {a,b} b 10 {a,b,c} {a,d,c} c ∞ 60 50 {a,d} {a,d} d 30 30 {a,e} {a,e} {a,e} {a,d,c,e} e 100 100 100 60 选...", "options": [], "section": "第七题", "knowledge_points": [["图结构", "图表示", 0.6000000000000001], ["线性表", "链表", 0.325]], "difficulty": "中等", "year": 2014, "raw_text": "(12分) (1)该有向图的邻接表为 0 a 1 10 3 30 4 100 b 2 50 1 2 c 4 10 d 2 20 3 4 e 3 60 步骤 i=1 i=2 i=3 i=4 节点 {a,b} b 10 {a,b,c} {a,d,c} c ∞ 60 50 {a,d} {a,d} d 30 30 {a,e} {a,e} {a,e} {a,d,c,e} e 100 100 100 60 选取关键 b d c e 路径点 S (b) (b,d) (b,c,d) (b,c,d,e)"}, {"id": "2014_数据结构2014A答案.pdf_第八题_1", "type": "填空题", "content": "{rb->next=p; rb=p; p=p->next;} Else { rc->next=p; rc=p; p=p->next;} } rb->next=rc->next=NULL; }", "options": [], "section": "第八题", "knowledge_points": [], "difficulty": "简单", "year": 2014, "raw_text": "{rb->next=p; rb=p; p=p->next;} Else { rc->next=p; rc=p; p=p->next;} } rb->next=rc->next=NULL; }"}, {"id": "2014_数据结构2014A答案.pdf_第九题_1", "type": "算法题", "content": "(6分) 算法编写如下： Int count(BinTreeNode * T) {if (T==NULL) retur n 0; else if (T->lchild!=null && T->rchild!=null) return (1+count(T->lchild)+ count(T->rchild)); else return (count(T->lchild)+ count(T->rch...", "options": [], "section": "第九题", "knowledge_points": [], "difficulty": "中等", "year": 2014, "raw_text": "(6分) 算法编写如下： Int count(BinTreeNode * T) {if (T==NULL) retur n 0; else if (T->lchild!=null && T->rchild!=null) return (1+count(T->lchild)+ count(T->rchild)); else return (count(T->lchild)+ count(T->rchild));"}, {"id": "2015_数据结构2015A.pdf_第九题_1", "type": "填空题", "content": "总分 教 场 考 得分 - - - - - - 燕 - - - - - - - - -", "options": [], "section": "第九题", "knowledge_points": [], "difficulty": "简单", "year": 2015, "raw_text": "总分 教  场  考 得分 - - - - - -    燕 - - - - - - - - -"}, {"id": "2015_数据结构2015A.pdf_第一题_1", "type": "填空题", "content": "在AOE网中，关键路径是指______________________________。 - - - - - - - - - - - - - - -", "options": [], "section": "第一题", "knowledge_points": [], "difficulty": "简单", "year": 2015, "raw_text": "在AOE网中，关键路径是指______________________________。 - - - - - - - - -  - - - - - -"}, {"id": "2015_数据结构2015A.pdf_第一题_1", "type": "选择题", "content": "在文件“局部有序”或文件长度较小的情况下，最佳内部排序的方法是( )", "options": ["A. 直接插入排序 B.冒泡排序 C.简单选择排序 D.快速排序"], "section": "第一题", "knowledge_points": [["排序算法", "简单排序", 0.4], ["排序算法", "高级排序", 0.13999999999999999]], "difficulty": "简单", "year": 2015, "raw_text": "在文件“局部有序”或文件长度较小的情况下，最佳内部排序的方法是( ) A.直接插入排序 B.冒泡排序 C.简单选择排序 D.快速排序"}, {"id": "2015_数据结构2015A.pdf_第二题_1", "type": "计算题", "content": "叉树是由某森林转换而成的，请画出森林。", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2015, "raw_text": "叉树是由某森林转换而成的，请画出森林。"}, {"id": "2015_数据结构2015A.pdf_第一题_1", "type": "计算题", "content": "棵哈夫曼树，并计算该 哈夫曼树的带权路径长度WPL。", "options": [], "section": "第一题", "knowledge_points": [["树结构", "哈夫曼树", 0.5]], "difficulty": "简单", "year": 2015, "raw_text": "棵哈夫曼树，并计算该 哈夫曼树的带权路径长度WPL。"}, {"id": "2015_数据结构2015A.pdf_第一题_1", "type": "简答题", "content": "什么是哈希表，请简述哈希表的查找过程(也可以用流程图表示) 号 : - - - - - - 座", "options": [], "section": "第一题", "knowledge_points": [["图结构", "图表示", 0.2], ["查找算法", "哈希表", 0.195]], "difficulty": "简单", "year": 2015, "raw_text": "什么是哈希表，请简述哈希表的查找过程(也可以用流程图表示) 号 : - - - - - - 座"}, {"id": "2015_数据结构2015A.pdf_第二题_1", "type": "填空题", "content": "趟分配和收集后得到的序列情况。", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2015, "raw_text": "趟分配和收集后得到的序列情况。"}, {"id": "2015_数据结构2015A.pdf_第八题_2", "type": "计算题", "content": "根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；", "options": [], "section": "第八题", "knowledge_points": [["图结构", "图表示", 0.4], ["树结构", "遍历", 0.325]], "difficulty": "简单", "year": 2015, "raw_text": "根据邻接表结构，写出从顶点a出发,深度优先遍历所得到的顶点序列；"}, {"id": "2015_数据结构2015A.pdf_第二题_1", "type": "填空题", "content": "项起的每个元素值是否等于其序号的平方减去其前驱结点 的值，若满足则返回ture，否则返回false. ＋ 第3页 共3页 ＋", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2015, "raw_text": "项起的每个元素值是否等于其序号的平方减去其前驱结点 的值，若满足则返回ture，否则返回false. ＋ 第3页 共3页 ＋"}, {"id": "2016_数据结构2016A.pdf_第九题_1", "type": "填空题", "content": "总分 教 - 场 - - 考 线 得分 - - - - - -", "options": [], "section": "第九题", "knowledge_points": [], "difficulty": "简单", "year": 2016, "raw_text": "总分 教 - 场 - - 考 线 得分 - - - - - -"}, {"id": "2016_数据结构2016A.pdf_第一题_1", "type": "填空题", "content": "在快速排序、堆排序、归并排序中，_________排序是稳定的。 - 年 - - 业 - 专 -", "options": [], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.41999999999999993]], "difficulty": "简单", "year": 2016, "raw_text": "在快速排序、堆排序、归并排序中，_________排序是稳定的。 - 年 - - 业 - 专 -"}, {"id": "2016_数据结构2016A.pdf_第三题_1", "type": "选择题", "content": "趟：2，5，10，12，16，88", "options": ["A. 希尔排序 B. 起泡排序 C.归并排序 D.基数排序"], "section": "第三题", "knowledge_points": [["排序算法", "高级排序", 0.13999999999999999], ["排序算法", "线性排序", 0.1]], "difficulty": "简单", "year": 2016, "raw_text": "趟：2，5，10，12，16，88 A. 希尔排序 B. 起泡排序 C.归并排序 D.基数排序"}, {"id": "2016_数据结构2016A.pdf_第二题_1", "type": "填空题", "content": "叉树转换成对应的森林 。 A A B C D E F ＋ 第2页 共4页 ＋", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2016, "raw_text": "叉树转换成对应的森林 。 A A B C D E F ＋  第2页 共4页 ＋"}, {"id": "2016_数据结构2016A.pdf_简答题_1", "type": "简答题", "content": "简答题(共 6 分) 给定字母 a,b,c,d,e 的使用频率为 0.09, 0.17, 0.2, 0.23, 0.31。设计以该权值为基础的哈夫曼树， 以及哈夫曼编码，并求其带权路径长度。 - - : - - 号 - -", "options": [], "section": "简答题", "knowledge_points": [["树结构", "哈夫曼树", 0.75]], "difficulty": "简单", "year": 2016, "raw_text": "简答题(共 6 分) 给定字母 a,b,c,d,e 的使用频率为 0.09, 0.17, 0.2, 0.23, 0.31。设计以该权值为基础的哈夫曼树， 以及哈夫曼编码，并求其带权路径长度。 - - : - - 号 - -"}, {"id": "2016_数据结构2016A.pdf_第一题_1", "type": "计算题", "content": "趟快速排序的结果。 - - - 3.序列的关键码为{80,70,33,65,24,56,48}，请用筛选法建立最小堆。 - - : - 号 - -", "options": [], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.13999999999999999]], "difficulty": "简单", "year": 2016, "raw_text": "趟快速排序的结果。 - - - 3.序列的关键码为{80,70,33,65,24,56,48}，请用筛选法建立最小堆。 - - : - 号 - -"}, {"id": "2016_数据结构2016A.pdf_简答题_1", "type": "简答题", "content": "简答题(共 6 分) 室 - - 教 - 设 散 列 表 的 长 度 为 13 ， 散 列 函 数 为 H(K)=K MOD 13 ， 给 定 的 关 键 字 序 列 为 ： 场 - - 19，14，23，01，68，20，84。试画出用线性探测再散列解决冲突 时所构成的散列表，并求 考 线 - 等概率情况下查找成功时的平均查找长 度。 - - - - -", "options": [], "section": "简答题", "knowledge_points": [["查找算法", "哈希表", 0.39]], "difficulty": "中等", "year": 2016, "raw_text": "简答题(共 6 分) 室 - - 教 - 设 散 列 表 的 长 度 为 13 ， 散 列 函 数 为 H(K)=K MOD 13 ， 给 定 的 关 键 字 序 列 为 ： 场 - - 19，14，23，01，68，20，84。试画出用线性探测再散列解决冲突 时所构成的散列表，并求 考 线 - 等概率情况下查找成功时的平均查找长 度。 - - - - -"}, {"id": "2016_数据结构2016A.pdf_简答题_1", "type": "简答题", "content": "写出其邻接表结构； - -", "options": [], "section": "简答题", "knowledge_points": [["图结构", "图表示", 0.2]], "difficulty": "简单", "year": 2016, "raw_text": "写出其邻接表结构； - -"}, {"id": "2016_数据结构2016A.pdf_简答题_2", "type": "简答题", "content": "根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列； - ： - 师 -", "options": [], "section": "简答题", "knowledge_points": [["图结构", "图表示", 0.4], ["树结构", "遍历", 0.325]], "difficulty": "简单", "year": 2016, "raw_text": "根据邻接表结构，写出从顶点A出发,深度优先遍历所得到的顶点序列； - ： - 师 -"}, {"id": "2016_数据结构2016A.pdf_第一题_1", "type": "算法题", "content": "算法，将带有头结点的非空单链表中数据域值最小的那个结点移到链表的最前面。 - - 要求：不得额外申请新的链结点。 - - - - - -", "options": [], "section": "第一题", "knowledge_points": [["线性表", "链表", 0.65]], "difficulty": "简单", "year": 2016, "raw_text": "算法，将带有头结点的非空单链表中数据域值最小的那个结点移到链表的最前面。 - - 要求：不得额外申请新的链结点。 - - - - - -"}, {"id": "2016_数据结构2016A.pdf_第二题_1", "type": "填空题", "content": "叉树，请在下划线处填上正确的内容。 - - - typedef struct node { - - char data； - - - struct node *leftchild, *rightchild； - - } BinTreeNode; - - - - - 号 : - BinTreeNode *Crt_BinTree( ) 学 ＋ 第3页 共4页 ＋ { char ch; BinTreeN...", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "困难", "year": 2016, "raw_text": "叉树，请在下划线处填上正确的内容。 - - - typedef struct node { - - char data； - - - struct node *leftchild, *rightchild； - - } BinTreeNode; - - - - - 号 : - BinTreeNode *Crt_BinTree( ) 学 ＋  第3页 共4页 ＋ { char ch; BinTreeNode *t； printf(\"please input data:\\n\")； scanf (\"%c\",&ch)； if (ch=='#') ___________ ; //输入#，表示空树 else { t=(BinTreeNode *) malloc(sizeof(BinTreeNode))； t->data = ch； ______________________； t->rightchild=Crt_BinTree( )； } return (t)； } ＋  第4页 共4页 ＋"}, {"id": "2016_数据结构2016A答案.pdf_选择题_1", "type": "选择题", "content": "选择题(共 10 题，每题 2 分，共 20 分) DCACD ABCDB", "options": [], "section": "选择题", "knowledge_points": [], "difficulty": "简单", "year": 2016, "raw_text": "选择题(共 10 题，每题 2 分，共 20 分) DCACD ABCDB"}, {"id": "2016_数据结构2016A答案.pdf_第二题_1", "type": "填空题", "content": "叉树转换成对应的森林 A C F A B E D", "options": [], "section": "第二题", "knowledge_points": [], "difficulty": "简单", "year": 2016, "raw_text": "叉树转换成对应的森林 A C F A B E D"}, {"id": "2016_数据结构2016A答案.pdf_简答题_1", "type": "简答题", "content": "简答题(共 6 分) 100 57 43 26 31 20 23 9 17 <PERSON><PERSON><PERSON> 编码：0.09:000 0.17:001 0.2:10 0.23:11 0.31:01 <PERSON><PERSON>man 编码带权路径长度 WPL=3*(0.09+0.17)+2*(0.2+0.23+0.31)=2.26", "options": [], "section": "简答题", "knowledge_points": [["树结构", "哈夫曼树", 0.75]], "difficulty": "简单", "year": 2016, "raw_text": "简答题(共 6 分) 100 57 43 26 31 20 23 9 17 <PERSON><PERSON><PERSON> 编码：0.09:000 0.17:001 0.2:10 0.23:11 0.31:01 <PERSON><PERSON>man 编码带权路径长度 WPL=3*(0.09+0.17)+2*(0.2+0.23+0.31)=2.26"}, {"id": "2016_数据结构2016A答案.pdf_第一题_1", "type": "计算题", "content": "趟快速排序的步骤： 46, 32, 55, 81, 65, 11, 25, 43 43, 32, 55, 81, 65, 11, 25, 46 43, 32, 46, 81, 65, 11, 25, 55 43, 32, 25, 81, 65, 11, 46, 55 43, 32, 25, 46, 65, 11, 81, 55 43, 32, 25, 11, 65, 46, 81, 55 43, ...", "options": [], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.13999999999999999]], "difficulty": "困难", "year": 2016, "raw_text": "趟快速排序的步骤： 46, 32, 55, 81, 65, 11, 25, 43 43, 32, 55, 81, 65, 11, 25, 46 43, 32, 46, 81, 65, 11, 25, 55 43, 32, 25, 81, 65, 11, 46, 55 43, 32, 25, 46, 65, 11, 81, 55 43, 32, 25, 11, 65, 46, 81, 55 43, 32, 25, 11,46, 65, 81, 55 3、关键码为{80,70,33,65,24,56,48}，用筛选法建立最小堆。 80 80 24 33 70 33 65 70 56 48 65 24 56 48 24 24 65 33 80 33 80 70 56 48 65 70 56 48"}, {"id": "2016_数据结构2016A答案.pdf_简答题_1", "type": "简答题", "content": "简答题(共 6 分) 将{19,14,23,01,68,20,84}依次存入散列表： 19 mod 13=6 14 mod 13=1 23 mod 13=10 1 mod 13=1 68 mod 13=3 20 mod 13=7 84 mod 13=6 0 1 2 3 4 5 6 7 8 9 10 11 12 14 1 68 19 20 84 23 各数据的查找长度分别为19：1次，14：1次，2...", "options": [], "section": "简答题", "knowledge_points": [["查找算法", "哈希表", 0.195]], "difficulty": "中等", "year": 2016, "raw_text": "简答题(共 6 分) 将{19,14,23,01,68,20,84}依次存入散列表： 19 mod 13=6 14 mod 13=1 23 mod 13=10 1 mod 13=1 68 mod 13=3 20 mod 13=7 84 mod 13=6 0 1 2 3 4 5 6 7 8 9 10 11 12 14 1 68 19 20 84 23 各数据的查找长度分别为19：1次，14：1次，23：1次，1：2次，68 :1次, 20：1次，84：3次 设查找等概率Pi=1/7, 因此平均查找长度为ASL=(1+1+1+2+1+1+3)/7=10/7"}, {"id": "2016_数据结构2016A答案.pdf_简答题_1", "type": "简答题", "content": "从顶点A出发深度优先遍历所得到的顶点序列为ABCFED (", "options": [], "section": "简答题", "knowledge_points": [["树结构", "遍历", 0.325], ["图结构", "图表示", 0.2]], "difficulty": "简单", "year": 2016, "raw_text": "从顶点A出发深度优先遍历所得到的顶点序列为ABCFED ("}, {"id": "2016_数据结构2016A答案.pdf_第一题_1", "type": "填空题", "content": "元素结点，则不需再操作 { pre->next=q->next; //将最小值结点从链表上摘下 q->next=L->next; //将 q 结点插到链表最前面 L->next=q; } }", "options": [], "section": "第一题", "knowledge_points": [["线性表", "链表", 0.325]], "difficulty": "简单", "year": 2016, "raw_text": "元素结点，则不需再操作 { pre->next=q->next; //将最小值结点从链表上摘下 q->next=L->next; //将 q 结点插到链表最前面 L->next=q; } }"}, {"id": "2016_数据结构2016A答案.pdf_简答题_1", "type": "简答题", "content": "简答题(共 4分) t=NULL； t->rightchild=Crt_BinTree( )；", "options": [], "section": "简答题", "knowledge_points": [], "difficulty": "简单", "year": 2016, "raw_text": "简答题(共 4分) t=NULL； t->rightchild=Crt_BinTree( )；"}, {"id": "2017_数据结构2017A.pdf_第八题_1", "type": "填空题", "content": "总分 室 - - 教 - 场 线 得分 - 考 - - - - -", "options": [], "section": "第八题", "knowledge_points": [], "difficulty": "简单", "year": 2017, "raw_text": "总分 室 - - 教 - 场 线 得分 - 考 - - - - -"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "填空题", "content": "棵树转换为_________。 - - - : - 级 -", "options": [], "section": "第一题", "knowledge_points": [], "difficulty": "简单", "year": 2017, "raw_text": "棵树转换为_________。 - - - : - 级 -"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "选择题", "content": "下面的排序算法中，稳定的算法是 ( ) 。", "options": ["A. 起泡排序 B.快速排序 C.希尔排序 D.堆排序"], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.27999999999999997]], "difficulty": "简单", "year": 2017, "raw_text": "下面的排序算法中，稳定的算法是 ( ) 。 A.起泡排序 B.快速排序 C.希尔排序 D.堆排序"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "简答题", "content": "种编码方案,试比较这两 - - - 种方案的优缺点。 - - - 号 : - - 座 - - ＋ 第2页 共4页 ＋ - - - - - - - : - 号 - 室 - - 教 - 线 场 - 考 - - - - - - - - 燕 - - 海 - - 张 - - - - ： - - 师 - - 教 - 课 - - 授 - - - 订 - - - - - - - - : - 级 - - 年 - 业...", "options": [], "section": "第一题", "knowledge_points": [["树结构", "哈夫曼树", 0.25]], "difficulty": "中等", "year": 2017, "raw_text": "种编码方案,试比较这两 - - - 种方案的优缺点。 - - - 号 : - - 座 - - ＋  第2页 共4页 ＋ - - - - - - - : - 号 - 室 - - 教 - 线 场 - 考 - - - - - - - - 燕 - - 海 - - 张 - - - - ： - - 师 - - 教 - 课 - - 授 - - - 订 - - - - - - - - : - 级 - - 年 - 业 - 专 - - - - - - - - - - - - - - : - 名 - 装 姓 - - - - - - - - - - - - - - - 号 : - 学"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "计算题", "content": "次分配和收集后得到的序列是多少？ 3.初始关键码序列为{5，4，24，11，8，12，13，3，16}，画出用筛选法建立的最大堆。", "options": [], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5]], "difficulty": "简单", "year": 2017, "raw_text": "次分配和收集后得到的序列是多少？ 3.初始关键码序列为{5，4，24，11，8，12，13，3，16}，画出用筛选法建立的最大堆。"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "计算题", "content": "组记录的关键字为{27，19， 23， 68，20，84，55，11，10，79}，用链地址法构造 哈希表，哈希函数为H(key)=key MOD 13, 并求等概率情况下查找成功时的平均查找长度。", "options": [], "section": "第一题", "knowledge_points": [["查找算法", "哈希表", 0.585]], "difficulty": "简单", "year": 2017, "raw_text": "组记录的关键字为{27，19， 23， 68，20，84，55，11，10，79}，用链地址法构造 哈希表，哈希函数为H(key)=key MOD 13, 并求等概率情况下查找成功时的平均查找长度。"}, {"id": "2017_数据结构2017A.pdf_第一题_1", "type": "填空题", "content": ") A 5 5 B C G C 6 7 1 D 2 D 3 F E 4 G B", "options": [], "section": "第一题", "knowledge_points": [], "difficulty": "简单", "year": 2017, "raw_text": ") A 5 5 B C G C 6 7 1 D 2 D 3 F E 4 G B"}, {"id": "2017_数据结构2017A.pdf_填空题_1", "type": "填空题", "content": "算法填空题(共 10分) 设A和B是两个单链表(带头结点)，其中元素递增有序。下面给出的算法功能是，由A和B 中公共相同的元素产生新的单链表C，要求不破坏", "options": ["A. B的结点。请在下划线处填上正确的内容。 typedef struct node { //链表结点 int data; //结点数据域 struct node * next; //结点链域 } Listnode; ＋  第3页 共4页 ＋ typedef Listnode * Linklist; Void common (Linklist A, Linklist B, Linklist &C) { Listnode *p=A->next, *q=B->next, *r, *s; C= (Listnode *)malloc(sizeof(Listnode)); r=C; while(p!=NULL &&q!=NULL) { if (p->data < q->data) p=p->next; else if ((p->data > q->data) q=q->next; else {s= (Listnode *)malloc(sizeof(Listnode)); s->data = p->data; _________; _________; _________; _________; } } r->next=NULL; } ＋  第4页 共4页 ＋"], "section": "填空题", "knowledge_points": [["线性表", "链表", 0.65]], "difficulty": "困难", "year": 2017, "raw_text": "算法填空题(共 10分) 设A和B是两个单链表(带头结点)，其中元素递增有序。下面给出的算法功能是，由A和B 中公共相同的元素产生新的单链表C，要求不破坏A、B的结点。请在下划线处填上正确的内容。 typedef struct node { //链表结点 int data; //结点数据域 struct node * next; //结点链域 } Listnode; ＋  第3页 共4页 ＋ typedef Listnode * Linklist; Void common (Linklist A, Linklist B, Linklist &C) { Listnode *p=A->next, *q=B->next, *r, *s; C= (Listnode *)malloc(sizeof(Listnode)); r=C; while(p!=NULL &&q!=NULL) { if (p->data < q->data) p=p->next; else if ((p->data > q->data) q=q->next; else {s= (Listnode *)malloc(sizeof(Listnode)); s->data = p->data; _________; _________; _________; _________; } } r->next=NULL; } ＋  第4页 共4页 ＋"}, {"id": "2018_数据结构2018A.pdf_第八题_1", "type": "填空题", "content": "总分 - - : - 号 线 得分 室 - - 教 - 场 - - 考 -", "options": [], "section": "第八题", "knowledge_points": [], "difficulty": "简单", "year": 2018, "raw_text": "总分 - - : - 号 线 得分 室 - - 教 - 场 - - 考 -"}, {"id": "2018_数据结构2018A.pdf_第一题_1", "type": "填空题", "content": "对n个关键字进行堆排序，最坏情况下其时间复杂度也为_________。 : - 级 - - 年 - 业 -", "options": [], "section": "第一题", "knowledge_points": [["树结构", "堆", 0.5], ["排序算法", "高级排序", 0.13999999999999999], ["算法分析", "复杂度", 0.12], ["排序算法", "排序分析", 0.1]], "difficulty": "简单", "year": 2018, "raw_text": "对n个关键字进行堆排序，最坏情况下其时间复杂度也为_________。 : - 级 - - 年 - 业 -"}, {"id": "2018_数据结构2018A.pdf_第一题_1", "type": "选择题", "content": "排序趟数与序列的初始状态有关的排序算法是( )。", "options": ["A. 直接插入排序 B.起泡排序 C.归并排序 D.简单选择排序"], "section": "第一题", "knowledge_points": [["排序算法", "简单排序", 0.30000000000000004], ["排序算法", "高级排序", 0.13999999999999999]], "difficulty": "简单", "year": 2018, "raw_text": "排序趟数与序列的初始状态有关的排序算法是( )。 A.直接插入排序 B.起泡排序 C.归并排序 D.简单选择排序"}, {"id": "2018_数据结构2018A.pdf_第二题_1", "type": "简答题", "content": "进制编码方案做比较。 ＋ 第2页 共3页 ＋", "options": [], "section": "第二题", "knowledge_points": [["树结构", "哈夫曼树", 0.25]], "difficulty": "中等", "year": 2018, "raw_text": "进制编码方案做比较。 ＋  第2页 共3页 ＋"}, {"id": "2018_数据结构2018A.pdf_第一题_1", "type": "计算题", "content": "趟排序结果。(要有实现的步骤) 2.对给定的关键字序列{110, 319, 007, 931, 264, 120, 122}进行基数排序，写出第2趟分配收 集后的关键字序列。", "options": [], "section": "第一题", "knowledge_points": [["排序算法", "线性排序", 0.1]], "difficulty": "简单", "year": 2018, "raw_text": "趟排序结果。(要有实现的步骤) 2.对给定的关键字序列{110, 319, 007, 931, 264, 120, 122}进行基数排序，写出第2趟分配收 集后的关键字序列。"}, {"id": "2018_数据结构2018A.pdf_第一题_1", "type": "计算题", "content": "维数组，哈希函数为H(key)=key MOD 7, 处理冲突采用线性探测再散列 法。请画出所构造的哈希表，并求等概率情况下查找成功时的平均查找长度。", "options": [], "section": "第一题", "knowledge_points": [["查找算法", "哈希表", 0.585], ["线性表", "数组", 0.3]], "difficulty": "简单", "year": 2018, "raw_text": "维数组，哈希函数为H(key)=key MOD 7, 处理冲突采用线性探测再散列 法。请画出所构造的哈希表，并求等概率情况下查找成功时的平均查找长度。"}, {"id": "2018_数据结构2018A.pdf_简答题_1", "type": "简答题", "content": "请写出其邻接表存储结构；", "options": [], "section": "简答题", "knowledge_points": [["图结构", "图表示", 0.2]], "difficulty": "简单", "year": 2018, "raw_text": "请写出其邻接表存储结构；"}, {"id": "2018_数据结构2018A.pdf_简答题_2", "type": "简答题", "content": "根据邻接表结构，写出从顶点A出发深度优先遍历所得到的顶点序列；", "options": [], "section": "简答题", "knowledge_points": [["图结构", "图表示", 0.4], ["树结构", "遍历", 0.325]], "difficulty": "简单", "year": 2018, "raw_text": "根据邻接表结构，写出从顶点A出发深度优先遍历所得到的顶点序列；"}, {"id": "2018_数据结构2018A.pdf_第一题_1", "type": "填空题", "content": "个结点，只能用常见的方法) typedef struct node { //链表结点 int data; //结点数据域 struct node * next; //结点指针域 } Listnode; Typedef Listnode * Linklist; ＋ 第3页 共3页 ＋", "options": [], "section": "第一题", "knowledge_points": [["线性表", "链表", 0.65]], "difficulty": "简单", "year": 2018, "raw_text": "个结点，只能用常见的方法) typedef struct node { //链表结点 int data; //结点数据域 struct node * next; //结点指针域 } Listnode; Typedef Listnode * Linklist; ＋  第3页 共3页 ＋"}]}